import { useState, useCallback } from 'react';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';

export interface PaymentMethodDetails {
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
    funding: string;
  };
}

export interface ChargeDetails {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  paid: boolean;
  refunded: boolean;
  amount_refunded: number;
  receipt_url?: string;
  billing_details?: {
    email?: string;
    name?: string;
    phone?: string;
    address?: any;
  };
  outcome?: {
    network_status: string;
    reason?: string;
    risk_level: string;
    seller_message: string;
    type: string;
  };
}

export interface StripePaymentIntentDetails {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  description?: string;
  receipt_email?: string;
  payment_method_types: string[];
}

export interface StripeSessionDetails {
  id: string;
  payment_status: string;
  status: string;
  amount_total?: number;
  currency?: string;
  created: number;
  expires_at?: number;
  customer_email?: string;
  customer_name?: string;
  payment_method_types: string[];
}

export interface PaymentDetails {
  orderId: string;
  orderAmount: number;
  commissionAmount: number;
  sellerAmount: number;
  walletAmountUsed: number;
  status: string;
  createdAt: any;
  paymentCompletedAt?: any;
  stripePaymentIntent?: StripePaymentIntentDetails;
  paymentMethod?: PaymentMethodDetails;
  charge?: ChargeDetails;
  stripeSession?: StripeSessionDetails;
  stripeError?: string;
  sessionError?: string;
}

export const usePaymentDetails = () => {
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPaymentDetails = useCallback(async (orderId: string) => {
    if (!orderId) {
      setError('Order ID is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const getPaymentDetails = httpsCallable(functions, 'getPaymentDetails');
      const result = await getPaymentDetails({ orderId });
      
      const details = result.data as PaymentDetails;
      setPaymentDetails(details);
    } catch (err) {
      console.error('Error fetching payment details:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch payment details');
      setPaymentDetails(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearPaymentDetails = useCallback(() => {
    setPaymentDetails(null);
    setError(null);
  }, []);

  return {
    paymentDetails,
    isLoading,
    error,
    fetchPaymentDetails,
    clearPaymentDetails,
  };
};
