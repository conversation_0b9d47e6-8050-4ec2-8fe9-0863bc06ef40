#!/usr/bin/env node

/**
 * 🔍 TEST SETUP VALIDATOR
 * Validates that all marketplace test components are properly configured
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
function loadEnvFile() {
  try {
    const envPath = path.join(process.cwd(), '.env');
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
          const [key, ...valueParts] = trimmedLine.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=');
            process.env[key] = value;
          }
        }
      }
    }
  } catch (error) {
    console.warn('Warning: Could not load .env file:', error.message);
  }
}

// Load environment variables at startup
loadEnvFile();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  const fullPath = path.join(process.cwd(), filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    const stats = fs.statSync(fullPath);
    log(`  ✅ ${description}: ${filePath} (${Math.round(stats.size / 1024)}KB)`, 'green');
    return true;
  } else {
    log(`  ❌ ${description}: ${filePath} - NOT FOUND`, 'red');
    return false;
  }
}

function checkDirectory(dirPath, description) {
  const fullPath = path.join(process.cwd(), dirPath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
  
  if (exists) {
    const files = fs.readdirSync(fullPath);
    log(`  ✅ ${description}: ${dirPath} (${files.length} files)`, 'green');
    return true;
  } else {
    log(`  ❌ ${description}: ${dirPath} - NOT FOUND`, 'red');
    return false;
  }
}

function checkPackageScript(scriptName, description) {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const hasScript = packageJson.scripts && packageJson.scripts[scriptName];
    
    if (hasScript) {
      log(`  ✅ ${description}: ${scriptName}`, 'green');
      return true;
    } else {
      log(`  ❌ ${description}: ${scriptName} - NOT FOUND`, 'red');
      return false;
    }
  } catch (error) {
    log(`  ❌ ${description}: Error reading package.json`, 'red');
    return false;
  }
}

function checkEnvironmentVar(varName, description) {
  const value = process.env[varName];
  
  if (value) {
    const maskedValue = value.length > 10 ? `${value.substring(0, 10)}...` : value;
    log(`  ✅ ${description}: ${varName} = ${maskedValue}`, 'green');
    return true;
  } else {
    log(`  ❌ ${description}: ${varName} - NOT SET`, 'red');
    return false;
  }
}

function validateTestFileContent(filePath, requiredContent, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const hasContent = requiredContent.every(item => content.includes(item));
    
    if (hasContent) {
      log(`  ✅ ${description}: Required content found`, 'green');
      return true;
    } else {
      const missing = requiredContent.filter(item => !content.includes(item));
      log(`  ❌ ${description}: Missing content - ${missing.join(', ')}`, 'red');
      return false;
    }
  } catch (error) {
    log(`  ❌ ${description}: Error reading file - ${error.message}`, 'red');
    return false;
  }
}

async function main() {
  log('\n' + '='.repeat(80), 'cyan');
  log('🔍 MARKETPLACE TEST SETUP VALIDATION', 'bright');
  log('='.repeat(80), 'cyan');
  log('');

  let allChecksPass = true;
  const results = {
    files: 0,
    directories: 0,
    scripts: 0,
    environment: 0,
    content: 0
  };

  // Check test files
  log('📁 Checking Test Files...', 'yellow');
  const testFiles = [
    ['tests/e2e-marketplace-flow.test.js', 'Main E2E Test Suite'],
    ['tests/helpers/test-data-factory.js', 'Test Data Factory'],
    ['tests/helpers/marketplace-test-helpers.js', 'Test Helper Functions'],
    ['tests/mocks/shippo-mock.js', 'Shippo API Mock'],
    ['tests/fixtures/test-users.json', 'Test User Fixtures'],
    ['tests/README-MARKETPLACE-TESTS.md', 'Test Documentation']
  ];

  for (const [file, desc] of testFiles) {
    if (checkFile(file, desc)) {
      results.files++;
    } else {
      allChecksPass = false;
    }
  }

  // Check directories
  log('\n📂 Checking Directories...', 'yellow');
  const directories = [
    ['tests', 'Tests Directory'],
    ['tests/helpers', 'Test Helpers Directory'],
    ['tests/mocks', 'Test Mocks Directory'],
    ['tests/fixtures', 'Test Fixtures Directory'],
    ['scripts', 'Scripts Directory']
  ];

  for (const [dir, desc] of directories) {
    if (checkDirectory(dir, desc)) {
      results.directories++;
    } else {
      allChecksPass = false;
    }
  }

  // Check package.json scripts
  log('\n📜 Checking Package Scripts...', 'yellow');
  const scripts = [
    ['test:marketplace', 'Marketplace Test Script'],
    ['test:marketplace:watch', 'Marketplace Watch Script'],
    ['test:e2e', 'E2E Test Script'],
    ['test:component', 'Component Test Script']
  ];

  for (const [script, desc] of scripts) {
    if (checkPackageScript(script, desc)) {
      results.scripts++;
    } else {
      allChecksPass = false;
    }
  }

  // Check environment variables
  log('\n🌍 Checking Environment Variables...', 'yellow');
  const envVars = [
    ['VITE_FIREBASE_API_KEY', 'Firebase API Key'],
    ['VITE_FIREBASE_PROJECT_ID', 'Firebase Project ID'],
    ['VITE_FIREBASE_AUTH_DOMAIN', 'Firebase Auth Domain'],
    ['VITE_FIREBASE_STORAGE_BUCKET', 'Firebase Storage Bucket'],
    ['VITE_FIREBASE_MESSAGING_SENDER_ID', 'Firebase Messaging Sender ID'],
    ['VITE_FIREBASE_APP_ID', 'Firebase App ID']
  ];

  for (const [envVar, desc] of envVars) {
    if (checkEnvironmentVar(envVar, desc)) {
      results.environment++;
    } else {
      allChecksPass = false;
    }
  }

  // Check test file content
  log('\n📝 Checking Test File Content...', 'yellow');
  const contentChecks = [
    {
      file: 'tests/e2e-marketplace-flow.test.js',
      required: ['describe', 'it', 'expect', 'MarketplaceTestHelpers', 'TestDataFactory'],
      description: 'Main test file structure'
    },
    {
      file: 'tests/helpers/test-data-factory.js',
      required: ['generateTestData', 'calculateExpectedCommission', 'scenarios'],
      description: 'Test data factory functions'
    },
    {
      file: 'tests/helpers/marketplace-test-helpers.js',
      required: ['createTestUsers', 'createListing', 'createCheckoutSession'],
      description: 'Test helper functions'
    },
    {
      file: 'tests/mocks/shippo-mock.js',
      required: ['ShippoMock', 'setup', 'generateMockLabel'],
      description: 'Shippo mock implementation'
    }
  ];

  for (const check of contentChecks) {
    if (validateTestFileContent(check.file, check.required, check.description)) {
      results.content++;
    } else {
      allChecksPass = false;
    }
  }

  // Check additional configuration files
  log('\n⚙️ Checking Configuration Files...', 'yellow');
  const configFiles = [
    ['vitest.config.ts', 'Vitest Configuration'],
    ['firebase.json', 'Firebase Configuration'],
    ['.firebaserc', 'Firebase Project Configuration'],
    ['package.json', 'Package Configuration']
  ];

  for (const [file, desc] of configFiles) {
    checkFile(file, desc);
  }

  // Print summary
  log('\n' + '='.repeat(80), 'cyan');
  log('📊 VALIDATION SUMMARY', 'bright');
  log('='.repeat(80), 'cyan');
  
  log(`📁 Test Files: ${results.files}/6`, results.files === 6 ? 'green' : 'red');
  log(`📂 Directories: ${results.directories}/5`, results.directories === 5 ? 'green' : 'red');
  log(`📜 Package Scripts: ${results.scripts}/4`, results.scripts === 4 ? 'green' : 'red');
  log(`🌍 Environment Variables: ${results.environment}/6`, results.environment === 6 ? 'green' : 'red');
  log(`📝 Content Validation: ${results.content}/4`, results.content === 4 ? 'green' : 'red');

  log('');
  
  if (allChecksPass) {
    log('🎉 ALL VALIDATION CHECKS PASSED!', 'green');
    log('✅ Your marketplace test setup is ready to run', 'green');
    log('');
    log('🚀 Next steps:', 'blue');
    log('  1. Run: npm run test:marketplace', 'blue');
    log('  2. Or run: node scripts/run-marketplace-tests.js', 'blue');
  } else {
    log('❌ SOME VALIDATION CHECKS FAILED', 'red');
    log('🔧 Please fix the issues above before running tests', 'yellow');
    log('');
    log('💡 Quick fixes:', 'blue');
    log('  1. Ensure all test files are created', 'blue');
    log('  2. Set up environment variables in .env', 'blue');
    log('  3. Install dependencies: npm install', 'blue');
  }
  
  log('='.repeat(80), 'cyan');
  
  process.exit(allChecksPass ? 0 : 1);
}

// Run validation
main().catch(error => {
  log(`\n❌ Validation failed: ${error.message}`, 'red');
  process.exit(1);
});
