"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecureDisputeManager = exports.DisputeStatus = exports.DisputeType = void 0;
const admin = __importStar(require("firebase-admin"));
const functions = __importStar(require("firebase-functions/v1"));
const schemas_1 = require("./validation/schemas");
const zod_1 = require("zod");
const order_status_1 = require("./order-status");
// Secure dispute handling system for Hive Campus
var DisputeType;
(function (DisputeType) {
    DisputeType["FAKE_DELIVERY"] = "fake_delivery";
    DisputeType["ITEM_NOT_RECEIVED"] = "item_not_received";
    DisputeType["ITEM_NOT_AS_DESCRIBED"] = "item_not_as_described";
    DisputeType["DAMAGED_ITEM"] = "damaged_item";
    DisputeType["WRONG_ITEM"] = "wrong_item";
    DisputeType["SELLER_UNRESPONSIVE"] = "seller_unresponsive";
    DisputeType["OTHER"] = "other";
})(DisputeType || (exports.DisputeType = DisputeType = {}));
var DisputeStatus;
(function (DisputeStatus) {
    DisputeStatus["OPEN"] = "open";
    DisputeStatus["UNDER_REVIEW"] = "under_review";
    DisputeStatus["RESOLVED_BUYER_FAVOR"] = "resolved_buyer_favor";
    DisputeStatus["RESOLVED_SELLER_FAVOR"] = "resolved_seller_favor";
    DisputeStatus["RESOLVED_PARTIAL"] = "resolved_partial";
    DisputeStatus["CLOSED"] = "closed";
})(DisputeStatus || (exports.DisputeStatus = DisputeStatus = {}));
// Dispute creation schema
const CreateDisputeSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1, 'Order ID is required'),
    type: zod_1.z.nativeEnum(DisputeType),
    description: zod_1.z.string().min(10, 'Description must be at least 10 characters').max(1000),
    evidence: zod_1.z.array(zod_1.z.object({
        type: zod_1.z.enum(['image', 'document', 'screenshot']),
        url: zod_1.z.string().url(),
        description: zod_1.z.string().max(200).optional()
    })).max(10).optional(),
    requestedResolution: zod_1.z.enum(['full_refund', 'partial_refund', 'replacement', 'store_credit'])
});
// Dispute resolution schema
const ResolveDisputeSchema = zod_1.z.object({
    disputeId: zod_1.z.string().min(1, 'Dispute ID is required'),
    resolution: zod_1.z.nativeEnum(DisputeStatus),
    adminNotes: zod_1.z.string().max(1000),
    refundAmount: zod_1.z.number().min(0).optional(),
    actionTaken: zod_1.z.string().max(500)
});
/**
 * Secure dispute management system
 */
class SecureDisputeManager {
    /**
     * Create a new dispute with validation
     */
    static async createDispute(buyerId, disputeData) {
        try {
            // Validate input
            const validatedData = (0, schemas_1.validateInput)(CreateDisputeSchema, disputeData);
            // Check if order exists and buyer is authorized
            const orderRef = admin.firestore().collection('orders').doc(validatedData.orderId);
            const orderDoc = await orderRef.get();
            if (!orderDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'Order not found');
            }
            const orderData = orderDoc.data();
            // Verify buyer authorization
            if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== buyerId) {
                throw new functions.https.HttpsError('permission-denied', 'Not authorized to dispute this order');
            }
            // Check if dispute window is still open (72 hours after delivery)
            const deliveredAt = orderData === null || orderData === void 0 ? void 0 : orderData.deliveredAt;
            if (deliveredAt) {
                const disputeWindowMs = 72 * 60 * 60 * 1000; // 72 hours
                const now = Date.now();
                const deliveryTime = deliveredAt.toMillis();
                if (now - deliveryTime > disputeWindowMs) {
                    throw new functions.https.HttpsError('failed-precondition', 'Dispute window has expired (72 hours after delivery)');
                }
            }
            // Check for existing disputes
            const existingDispute = await admin.firestore()
                .collection('disputes')
                .where('orderId', '==', validatedData.orderId)
                .where('status', 'in', [DisputeStatus.OPEN, DisputeStatus.UNDER_REVIEW])
                .limit(1)
                .get();
            if (!existingDispute.empty) {
                throw new functions.https.HttpsError('already-exists', 'An active dispute already exists for this order');
            }
            // Create dispute record
            const disputeRef = admin.firestore().collection('disputes').doc();
            const dispute = {
                id: disputeRef.id,
                orderId: validatedData.orderId,
                buyerId,
                sellerId: orderData.sellerId,
                type: validatedData.type,
                status: DisputeStatus.OPEN,
                description: validatedData.description,
                evidence: validatedData.evidence || [],
                requestedResolution: validatedData.requestedResolution,
                createdAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
            };
            // Use transaction to create dispute and update order
            await admin.firestore().runTransaction(async (transaction) => {
                // Create dispute
                transaction.set(disputeRef, dispute);
                // Update order status to disputed
                await order_status_1.SecureOrderStatusManager.updateOrderStatus(validatedData.orderId, order_status_1.OrderStatus.DISPUTED, buyerId, {
                    reason: `Dispute created: ${validatedData.type}`,
                    isSystemUpdate: true
                });
            });
            // Send admin notification
            await this.notifyAdminOfDispute(dispute);
            // Log dispute creation
            console.log(`🚨 Dispute created: ${disputeRef.id} for order ${validatedData.orderId} by ${buyerId}`);
            return { success: true, disputeId: disputeRef.id };
        }
        catch (error) {
            console.error('❌ Error creating dispute:', error);
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            throw new functions.https.HttpsError('internal', 'Failed to create dispute');
        }
    }
    /**
     * Resolve a dispute (admin only)
     */
    static async resolveDispute(adminId, resolutionData) {
        try {
            // Validate input
            const validatedData = (0, schemas_1.validateInput)(ResolveDisputeSchema, resolutionData);
            const disputeRef = admin.firestore().collection('disputes').doc(validatedData.disputeId);
            const result = await admin.firestore().runTransaction(async (transaction) => {
                const disputeDoc = await transaction.get(disputeRef);
                if (!disputeDoc.exists) {
                    throw new functions.https.HttpsError('not-found', 'Dispute not found');
                }
                const disputeData = disputeDoc.data();
                // Check if dispute is still open
                if (![DisputeStatus.OPEN, DisputeStatus.UNDER_REVIEW].includes(disputeData.status)) {
                    throw new functions.https.HttpsError('failed-precondition', 'Dispute is already resolved');
                }
                // Update dispute with resolution
                const updateData = {
                    status: validatedData.resolution,
                    resolvedAt: admin.firestore.Timestamp.now(),
                    resolvedBy: adminId,
                    adminNotes: validatedData.adminNotes,
                    actionTaken: validatedData.actionTaken,
                    updatedAt: admin.firestore.Timestamp.now()
                };
                if (validatedData.refundAmount !== undefined) {
                    updateData.refundAmount = validatedData.refundAmount;
                }
                transaction.update(disputeRef, updateData);
                // Update order status based on resolution
                let newOrderStatus;
                switch (validatedData.resolution) {
                    case DisputeStatus.RESOLVED_BUYER_FAVOR:
                        newOrderStatus = order_status_1.OrderStatus.REFUNDED;
                        break;
                    case DisputeStatus.RESOLVED_SELLER_FAVOR:
                        newOrderStatus = order_status_1.OrderStatus.COMPLETED;
                        break;
                    case DisputeStatus.RESOLVED_PARTIAL:
                        newOrderStatus = order_status_1.OrderStatus.COMPLETED;
                        break;
                    default:
                        newOrderStatus = order_status_1.OrderStatus.ADMIN_REVIEW;
                }
                // Update order status
                await order_status_1.SecureOrderStatusManager.updateOrderStatus(disputeData.orderId, newOrderStatus, adminId, {
                    reason: `Dispute resolved: ${validatedData.resolution}`,
                    adminOverride: true,
                    isSystemUpdate: true
                });
                return { success: true, message: `Dispute resolved: ${validatedData.resolution}` };
            });
            // Handle post-resolution actions
            await this.handleDisputeResolution(validatedData.disputeId, validatedData.resolution, validatedData.refundAmount);
            console.log(`✅ Dispute ${validatedData.disputeId} resolved by admin ${adminId}: ${validatedData.resolution}`);
            return result;
        }
        catch (error) {
            console.error('❌ Error resolving dispute:', error);
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            throw new functions.https.HttpsError('internal', 'Failed to resolve dispute');
        }
    }
    /**
     * Get disputes for admin dashboard
     */
    static async getDisputesForAdmin(status, limit = 20) {
        try {
            let query = admin.firestore()
                .collection('disputes')
                .orderBy('createdAt', 'desc')
                .limit(limit);
            if (status) {
                query = query.where('status', '==', status);
            }
            const snapshot = await query.get();
            return snapshot.docs.map(doc => doc.data());
        }
        catch (error) {
            console.error('❌ Error getting disputes for admin:', error);
            throw new functions.https.HttpsError('internal', 'Failed to get disputes');
        }
    }
    /**
     * Check for fake delivery patterns using AI
     */
    static async checkForFakeDeliveryPatterns(orderId) {
        try {
            // Get recent orders from the same seller
            const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
            if (!orderDoc.exists)
                return false;
            const orderData = orderDoc.data();
            const sellerId = orderData === null || orderData === void 0 ? void 0 : orderData.sellerId;
            // Get seller's recent orders
            const recentOrders = await admin.firestore()
                .collection('orders')
                .where('sellerId', '==', sellerId)
                .where('status', '==', order_status_1.OrderStatus.DELIVERED)
                .orderBy('deliveredAt', 'desc')
                .limit(10)
                .get();
            // Check for suspicious patterns
            const orders = recentOrders.docs.map(doc => doc.data());
            // Pattern 1: Too many quick deliveries
            const quickDeliveries = orders.filter(order => {
                var _a, _b;
                const shippedAt = (_a = order.shippedAt) === null || _a === void 0 ? void 0 : _a.toMillis();
                const deliveredAt = (_b = order.deliveredAt) === null || _b === void 0 ? void 0 : _b.toMillis();
                return deliveredAt && shippedAt && (deliveredAt - shippedAt) < (2 * 60 * 60 * 1000); // Less than 2 hours
            });
            if (quickDeliveries.length >= 3) {
                console.warn(`🚨 Suspicious delivery pattern detected for seller ${sellerId}: ${quickDeliveries.length} quick deliveries`);
                return true;
            }
            // Pattern 2: High dispute rate
            const disputes = await admin.firestore()
                .collection('disputes')
                .where('sellerId', '==', sellerId)
                .where('type', '==', DisputeType.FAKE_DELIVERY)
                .get();
            if (disputes.size >= 2) {
                console.warn(`🚨 High fake delivery dispute rate for seller ${sellerId}: ${disputes.size} disputes`);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error('❌ Error checking fake delivery patterns:', error);
            return false;
        }
    }
    /**
     * Notify admin of new dispute
     */
    static async notifyAdminOfDispute(dispute) {
        try {
            await admin.firestore().collection('admin_notifications').add({
                type: 'dispute_created',
                title: `New Dispute: ${dispute.type}`,
                message: `Order ${dispute.orderId} has been disputed by buyer`,
                disputeId: dispute.id,
                orderId: dispute.orderId,
                priority: 'high',
                createdAt: admin.firestore.Timestamp.now(),
                read: false,
                actionUrl: `/admin/disputes/${dispute.id}`
            });
        }
        catch (error) {
            console.error('❌ Error notifying admin of dispute:', error);
        }
    }
    /**
     * Handle post-resolution actions
     */
    static async handleDisputeResolution(disputeId, resolution, refundAmount) {
        try {
            // Send notifications to buyer and seller
            // Process refunds if applicable
            // Update seller reputation if needed
            console.log(`📧 Processing post-resolution actions for dispute ${disputeId}: ${resolution}`);
        }
        catch (error) {
            console.error('❌ Error in post-resolution actions:', error);
        }
    }
}
exports.SecureDisputeManager = SecureDisputeManager;
