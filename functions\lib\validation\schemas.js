"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationSchemas = exports.validateInput = exports.RateLimitSchema = exports.AdminNotificationSchema = exports.AddressSchema = exports.UserNameSchema = exports.UserEmailSchema = exports.WalletSettingsSchema = exports.WalletAmountSchema = exports.CheckoutSessionSchema = exports.ReleaseFundsSchema = exports.SecretCodeSchema = exports.OrderIdSchema = exports.ChatMessageSchema = exports.MessageTextSchema = exports.ListingCreateSchema = exports.ListingPriceSchema = exports.ListingDescriptionSchema = exports.ListingTitleSchema = exports.AdminPinSchema = void 0;
const zod_1 = require("zod");
// Security-focused validation schemas for Hive Campus
// Admin PIN validation - now requires 12 digits minimum
exports.AdminPinSchema = zod_1.z.object({
    pin: zod_1.z.string()
        .regex(/^\d{12,}$/, 'PIN must be at least 12 digits')
        .min(12, 'PIN must be at least 12 digits')
        .max(20, 'PIN cannot exceed 20 digits')
});
// Listing validation schemas
exports.ListingTitleSchema = zod_1.z.string()
    .min(1, 'Title is required')
    .max(80, 'Title cannot exceed 80 characters')
    .regex(/^[^<>]*$/, 'Title cannot contain HTML tags')
    .trim();
exports.ListingDescriptionSchema = zod_1.z.string()
    .max(500, 'Description cannot exceed 500 characters')
    .regex(/^(?!.*<script).*$/i, 'Description cannot contain script tags')
    .trim();
exports.ListingPriceSchema = zod_1.z.number()
    .positive('Price must be positive')
    .max(10000, 'Price cannot exceed $10,000')
    .multipleOf(0.01, 'Price must be in cents');
exports.ListingCreateSchema = zod_1.z.object({
    title: exports.ListingTitleSchema,
    description: exports.ListingDescriptionSchema,
    price: exports.ListingPriceSchema,
    category: zod_1.z.enum(['textbooks', 'electronics', 'furniture', 'clothing', 'other']),
    condition: zod_1.z.enum(['new', 'like_new', 'good', 'fair', 'poor']),
    university: zod_1.z.string().min(1, 'University is required'),
    visibility: zod_1.z.enum(['public', 'university']).default('university')
});
// Message validation schemas
exports.MessageTextSchema = zod_1.z.string()
    .min(1, 'Message cannot be empty')
    .max(1000, 'Message cannot exceed 1000 characters')
    .regex(/^(?!.*<script).*$/i, 'Message cannot contain script tags')
    .trim();
exports.ChatMessageSchema = zod_1.z.object({
    text: exports.MessageTextSchema,
    receiverId: zod_1.z.string().min(1, 'Receiver ID is required'),
    chatId: zod_1.z.string().min(1, 'Chat ID is required')
});
// Order validation schemas
exports.OrderIdSchema = zod_1.z.string()
    .min(1, 'Order ID is required')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Invalid order ID format');
exports.SecretCodeSchema = zod_1.z.string()
    .regex(/^[a-fA-F0-9]{6}$/, 'Secret code must be 6 hexadecimal characters');
exports.ReleaseFundsSchema = zod_1.z.object({
    orderId: exports.OrderIdSchema,
    secretCode: exports.SecretCodeSchema
});
// Payment validation schemas
exports.CheckoutSessionSchema = zod_1.z.object({
    listingId: zod_1.z.string().min(1, 'Listing ID is required'),
    quantity: zod_1.z.number().int().positive().max(10).default(1),
    useWalletBalance: zod_1.z.boolean().default(false),
    orderDetails: zod_1.z.object({
        price: exports.ListingPriceSchema.optional(),
        shippingFee: zod_1.z.number().min(0).max(100).default(0),
        appliedWalletCredit: zod_1.z.number().min(0).default(0)
    }).optional()
});
// Wallet validation schemas
exports.WalletAmountSchema = zod_1.z.number()
    .positive('Amount must be positive')
    .max(1000, 'Amount cannot exceed $1000')
    .multipleOf(0.01, 'Amount must be in cents');
exports.WalletSettingsSchema = zod_1.z.object({
    signupBonus: zod_1.z.number().min(0).max(100),
    referralBonus: zod_1.z.number().min(0).max(100),
    enableSignupBonus: zod_1.z.boolean(),
    enableReferralBonus: zod_1.z.boolean()
});
// User validation schemas
exports.UserEmailSchema = zod_1.z.string()
    .email('Invalid email format')
    .regex(/\.(edu|ac\.|edu\.)/, 'Must be an educational email address');
exports.UserNameSchema = zod_1.z.string()
    .min(1, 'Name is required')
    .max(50, 'Name cannot exceed 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
    .trim();
// Shipping validation schemas
exports.AddressSchema = zod_1.z.object({
    name: exports.UserNameSchema,
    street1: zod_1.z.string().min(1, 'Street address is required').max(100),
    street2: zod_1.z.string().max(100).optional(),
    city: zod_1.z.string().min(1, 'City is required').max(50),
    state: zod_1.z.string().min(2, 'State is required').max(2),
    zip: zod_1.z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
    country: zod_1.z.string().length(2, 'Country must be 2-letter code').default('US')
});
// Admin notification validation
exports.AdminNotificationSchema = zod_1.z.object({
    type: zod_1.z.enum(['user_signup', 'payment_completed', 'payment_failed', 'order_created', 'listing_created']),
    title: zod_1.z.string().min(1).max(100),
    message: zod_1.z.string().min(1).max(500),
    userId: zod_1.z.string().optional(),
    username: zod_1.z.string().optional(),
    orderId: zod_1.z.string().optional(),
    amount: zod_1.z.number().optional(),
    metadata: zod_1.z.record(zod_1.z.unknown()).default({}),
    actionUrl: zod_1.z.string().url().optional()
});
// Rate limiting schemas
exports.RateLimitSchema = zod_1.z.object({
    identifier: zod_1.z.string().min(1), // IP or user ID
    action: zod_1.z.string().min(1), // Action type (login, pin_verify, etc.)
    windowMs: zod_1.z.number().positive().default(15 * 60 * 1000), // 15 minutes
    maxAttempts: zod_1.z.number().positive().default(5)
});
// Helper function to validate and sanitize input
const validateInput = (schema, data) => {
    try {
        return schema.parse(data);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Validation failed: ${messages.join(', ')}`);
        }
        throw error;
    }
};
exports.validateInput = validateInput;
// Export all schemas for easy import
exports.ValidationSchemas = {
    AdminPin: exports.AdminPinSchema,
    ListingTitle: exports.ListingTitleSchema,
    ListingDescription: exports.ListingDescriptionSchema,
    ListingPrice: exports.ListingPriceSchema,
    ListingCreate: exports.ListingCreateSchema,
    MessageText: exports.MessageTextSchema,
    ChatMessage: exports.ChatMessageSchema,
    OrderId: exports.OrderIdSchema,
    SecretCode: exports.SecretCodeSchema,
    ReleaseFunds: exports.ReleaseFundsSchema,
    CheckoutSession: exports.CheckoutSessionSchema,
    WalletAmount: exports.WalletAmountSchema,
    WalletSettings: exports.WalletSettingsSchema,
    UserEmail: exports.UserEmailSchema,
    UserName: exports.UserNameSchema,
    Address: exports.AddressSchema,
    AdminNotification: exports.AdminNotificationSchema,
    RateLimit: exports.RateLimitSchema
};
