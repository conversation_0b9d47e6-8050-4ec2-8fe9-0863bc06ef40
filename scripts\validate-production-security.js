#!/usr/bin/env node

/**
 * 🔐 PRODUCTION SECURITY VALIDATION SCRIPT
 * Validates all security configurations before production deployment
 */

import fs from 'fs';
import path from 'path';

const REQUIRED_ENV_VARS = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID',
  'VITE_STRIPE_PUBLISHABLE_KEY',
  'VITE_SENTRY_DSN'
];

const SECURITY_PATTERNS = {
  STRIPE_LIVE_KEY: /^pk_live_/,
  FIREBASE_PROJECT_PROD: /^(?!.*test|.*dev|.*staging)/i,
  SENTRY_DSN: /^https:\/\/.*@sentry\.io/
};

class ProductionSecurityValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
  }

  validateEnvironmentVariables() {
    console.log('🔍 Validating environment variables...');
    
    // Check if .env.production exists
    const envPath = path.join(process.cwd(), '.env.production');
    if (!fs.existsSync(envPath)) {
      this.errors.push('❌ .env.production file not found');
      return;
    }

    // Read environment variables
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value && !key.startsWith('#')) {
        envVars[key.trim()] = value.trim();
      }
    });

    // Check required variables
    REQUIRED_ENV_VARS.forEach(varName => {
      if (!envVars[varName]) {
        this.errors.push(`❌ Missing required environment variable: ${varName}`);
      } else if (envVars[varName].includes('your_') || envVars[varName].includes('placeholder')) {
        this.errors.push(`❌ Environment variable ${varName} contains placeholder value`);
      } else {
        this.passed.push(`✅ ${varName} is configured`);
      }
    });

    // Validate specific patterns
    if (envVars.VITE_STRIPE_PUBLISHABLE_KEY) {
      if (!SECURITY_PATTERNS.STRIPE_LIVE_KEY.test(envVars.VITE_STRIPE_PUBLISHABLE_KEY)) {
        this.warnings.push(`⚠️ Stripe key should start with pk_live_ for production`);
      } else {
        this.passed.push(`✅ Stripe live key format is correct`);
      }
    }

    if (envVars.VITE_SENTRY_DSN) {
      if (!SECURITY_PATTERNS.SENTRY_DSN.test(envVars.VITE_SENTRY_DSN)) {
        this.warnings.push(`⚠️ Sentry DSN format may be incorrect`);
      } else {
        this.passed.push(`✅ Sentry DSN format is correct`);
      }
    }
  }

  validateFirebaseConfig() {
    console.log('🔥 Validating Firebase configuration...');
    
    const configPath = path.join(process.cwd(), 'src/firebase/config.ts');
    if (!fs.existsSync(configPath)) {
      this.errors.push('❌ Firebase config file not found');
      return;
    }

    const configContent = fs.readFileSync(configPath, 'utf8');
    
    // Check for hardcoded values
    if (configContent.includes('AIzaSy') && !configContent.includes('import.meta.env')) {
      this.errors.push('❌ Hardcoded Firebase API key found in config');
    } else {
      this.passed.push('✅ Firebase config uses environment variables');
    }

    // Check for proper environment variable usage
    REQUIRED_ENV_VARS.filter(v => v.startsWith('VITE_FIREBASE')).forEach(varName => {
      if (!configContent.includes(varName)) {
        this.warnings.push(`⚠️ ${varName} not found in Firebase config`);
      }
    });
  }

  validateSecurityHeaders() {
    console.log('🛡️ Validating security headers...');
    
    const firebaseConfigPath = path.join(process.cwd(), 'firebase.json');
    if (!fs.existsSync(firebaseConfigPath)) {
      this.errors.push('❌ firebase.json not found');
      return;
    }

    const firebaseConfig = JSON.parse(fs.readFileSync(firebaseConfigPath, 'utf8'));
    
    if (!firebaseConfig.hosting || !firebaseConfig.hosting.headers) {
      this.errors.push('❌ No security headers configured in firebase.json');
      return;
    }

    const headers = firebaseConfig.hosting.headers;
    const requiredHeaders = [
      'Content-Security-Policy',
      'Strict-Transport-Security',
      'X-Content-Type-Options',
      'X-Frame-Options',
      'X-XSS-Protection'
    ];

    const configuredHeaders = headers.flatMap(h => 
      h.headers ? h.headers.map(header => header.key) : []
    );

    requiredHeaders.forEach(header => {
      if (configuredHeaders.includes(header)) {
        this.passed.push(`✅ ${header} header configured`);
      } else {
        this.errors.push(`❌ Missing security header: ${header}`);
      }
    });

    // Check HSTS configuration
    const hstsHeader = headers.flatMap(h => h.headers || [])
      .find(h => h.key === 'Strict-Transport-Security');
    
    if (hstsHeader && hstsHeader.value.includes('max-age') && hstsHeader.value.includes('preload')) {
      this.passed.push('✅ HSTS properly configured with preload');
    } else if (hstsHeader) {
      this.warnings.push('⚠️ HSTS should include preload directive');
    }
  }

  validateGitignore() {
    console.log('📝 Validating .gitignore configuration...');
    
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (!fs.existsSync(gitignorePath)) {
      this.errors.push('❌ .gitignore file not found');
      return;
    }

    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    
    const requiredIgnores = ['.env.production', '.env.local', '*.log'];
    
    requiredIgnores.forEach(pattern => {
      if (gitignoreContent.includes(pattern)) {
        this.passed.push(`✅ ${pattern} is in .gitignore`);
      } else {
        this.errors.push(`❌ ${pattern} should be in .gitignore`);
      }
    });
  }

  validateDependencies() {
    console.log('📦 Validating dependencies...');
    
    const packagePath = path.join(process.cwd(), 'package.json');
    if (!fs.existsSync(packagePath)) {
      this.errors.push('❌ package.json not found');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Check for security-related dependencies
    const securityDeps = ['@sentry/react', 'firebase'];
    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    securityDeps.forEach(dep => {
      if (allDeps[dep]) {
        this.passed.push(`✅ ${dep} dependency found`);
      } else {
        this.warnings.push(`⚠️ ${dep} dependency not found`);
      }
    });

    // Check for outdated Firebase SDK
    if (allDeps.firebase && allDeps.firebase.startsWith('^9.')) {
      this.passed.push('✅ Firebase SDK version 9+ (modern)');
    } else if (allDeps.firebase) {
      this.warnings.push('⚠️ Consider upgrading to Firebase SDK v9+');
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🔐 PRODUCTION SECURITY VALIDATION REPORT');
    console.log('='.repeat(60));
    
    console.log(`\n✅ PASSED CHECKS (${this.passed.length}):`);
    this.passed.forEach(check => console.log(`   ${check}`));
    
    if (this.warnings.length > 0) {
      console.log(`\n⚠️ WARNINGS (${this.warnings.length}):`);
      this.warnings.forEach(warning => console.log(`   ${warning}`));
    }
    
    if (this.errors.length > 0) {
      console.log(`\n❌ ERRORS (${this.errors.length}):`);
      this.errors.forEach(error => console.log(`   ${error}`));
    }
    
    console.log('\n' + '='.repeat(60));
    
    if (this.errors.length === 0) {
      console.log('🎉 SECURITY VALIDATION PASSED!');
      console.log('✅ Ready for production deployment');
      return true;
    } else {
      console.log('🚨 SECURITY VALIDATION FAILED!');
      console.log('❌ Fix all errors before production deployment');
      return false;
    }
  }

  async run() {
    console.log('🔐 Starting Production Security Validation...\n');
    
    this.validateEnvironmentVariables();
    this.validateFirebaseConfig();
    this.validateSecurityHeaders();
    this.validateGitignore();
    this.validateDependencies();
    
    return this.generateReport();
  }
}

// Run validation
const validator = new ProductionSecurityValidator();
validator.run().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Validation failed:', error);
  process.exit(1);
});
