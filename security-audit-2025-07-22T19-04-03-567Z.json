{"timestamp": "2025-07-22T19:03:24.451Z", "overallScore": 73, "passed": 22, "failed": 4, "warnings": 4, "categories": {"firebase-rules": {"tests": [{"test": "No unsafe global read/write rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:03:24.793Z"}, {"test": "Authentication checks implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:03:24.793Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:03:24.794Z"}, {"test": "User ownership validation present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:03:24.794Z"}, {"test": "Input validation in security rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:03:24.794Z"}], "score": 100}, "secrets": {"tests": [{"test": "No hardcoded secrets in source code", "status": "FAIL", "severity": "Critical", "details": null, "timestamp": "2025-07-22T19:03:46.248Z"}, {"test": "All required environment variables present", "status": "FAIL", "severity": "Critical", "details": "Missing: VITE_FIREBASE_API_KEY, VITE_FIREBASE_AUTH_DOMAIN, VITE_FIREBASE_PROJECT_ID, VITE_FIREBASE_STORAGE_BUCKET, VITE_FIREBASE_MESSAGING_SENDER_ID, VITE_FIREBASE_APP_ID, VITE_STRIPE_PUBLISHABLE_KEY", "timestamp": "2025-07-22T19:03:46.249Z"}, {"test": "No .env files committed to repository", "status": "FAIL", "severity": "High", "details": "Found: .env, .env.production", "timestamp": "2025-07-22T19:03:46.249Z"}], "score": 0}, "dependencies": {"tests": [{"test": "Dependency audit failed: Command failed: npm audit --json", "status": "FAIL", "severity": "Critical", "details": null, "timestamp": "2025-07-22T19:03:52.484Z"}, {"test": "Some dependencies may be outdated", "status": "WARN", "severity": "Low", "details": null, "timestamp": "2025-07-22T19:04:03.116Z"}], "score": 0}, "client-security": {"tests": [{"test": "Security utilities implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.117Z"}, {"test": "Input sanitization implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.117Z"}, {"test": "HTTPS enforcement implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.117Z"}, {"test": "Client-side rate limiting implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.117Z"}, {"test": "Security initialization in main app", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.118Z"}], "score": 100}, "network-security": {"tests": [{"test": "Security headers configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:04:03.129Z"}, {"test": "HTTPS configuration present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.129Z"}, {"test": "Firebase hosting security headers configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.347Z"}, {"test": "HSTS header configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:04:03.348Z"}], "score": 50}, "data-privacy": {"tests": [{"test": "Privacy policy page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.348Z"}, {"test": "Terms and conditions page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.349Z"}, {"test": "Consent management implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.388Z"}, {"test": "Data deletion policies in Firebase rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.388Z"}], "score": 100}, "csp": {"tests": [{"test": "CSP manager implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.389Z"}, {"test": "Nonce generation for inline scripts", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.389Z"}, {"test": "CSP meta tag in HTML", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:04:03.561Z"}], "score": 67}, "authentication": {"tests": [{"test": "Authentication context implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.562Z"}, {"test": "Protected route component implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.562Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.563Z"}, {"test": "Firebase Auth domain configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:04:03.563Z"}], "score": 100}}}