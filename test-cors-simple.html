<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple CORS Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Simple CORS Test</h1>
    <p>Testing CORS headers for Firebase Cloud Functions</p>
    
    <button onclick="testCORS()">Test CORS Headers</button>
    <button onclick="testTestFunction()">Test testStripeConnect Function</button>
    <button onclick="clearResults()">Clear Results</button>

    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testCORS() {
            addResult('🧪 Testing CORS headers...', 'info');
            
            try {
                // Test with a simple OPTIONS request first
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/testStripeConnect', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                addResult(`✅ OPTIONS request successful: ${response.status}`, 'success');
                
                // Check CORS headers
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                };

                addResult(`CORS Headers: <pre>${JSON.stringify(corsHeaders, null, 2)}</pre>`, 'info');

                if (corsHeaders['Access-Control-Allow-Origin']) {
                    addResult('✅ CORS headers are present', 'success');
                } else {
                    addResult('❌ CORS headers are missing', 'error');
                }

            } catch (error) {
                addResult(`❌ CORS test failed: ${error.message}`, 'error');
                
                if (error.message.includes('CORS') || error.message.includes('Access-Control-Allow-Origin')) {
                    addResult('🚨 CORS error detected - functions may not have proper CORS configuration', 'error');
                }
            }
        }

        async function testTestFunction() {
            addResult('🧪 Testing testStripeConnect function...', 'info');
            
            try {
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/testStripeConnect', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Function call successful', 'success');
                    addResult(`Response: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ Function call failed: ${response.status} ${response.statusText}`, 'error');
                }

            } catch (error) {
                addResult(`❌ Function test failed: ${error.message}`, 'error');
                
                if (error.message.includes('CORS') || error.message.includes('Access-Control-Allow-Origin')) {
                    addResult('🚨 CORS error detected', 'error');
                } else if (error.message.includes('Failed to fetch')) {
                    addResult('🌐 Network error - check if functions are deployed', 'error');
                }
            }
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            addResult(`Testing from origin: ${window.location.origin}`, 'info');
            setTimeout(testTestFunction, 1000);
        });
    </script>
</body>
</html>
