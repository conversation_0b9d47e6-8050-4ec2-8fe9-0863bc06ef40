import React, { useState, useEffect } from 'react';
import {
  Wallet as WalletIcon,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  ArrowDownLeft,
  Loader,
  AlertCircle,
  Gift,
  Copy,
  CheckCircle,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
// import { formatDistanceToNow } from 'date-fns';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { LocalWalletService } from '../services/walletService';
import { formatTimestamp, formatRelativeTime } from '../utils/timestamp';

interface WalletTransaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  source: 'admin_grant' | 'referral_bonus' | 'signup_bonus' | 'cashback' | 'purchase_deduction';
  orderId?: string;
  grantedBy?: string;
  referralUserId?: string;
  expiresAt?: any;
  isExpired?: boolean;
  createdAt: any;
}

interface WalletData {
  userId: string;
  balance: number;
  referralCode: string;
  usedReferral: boolean;
  history: WalletTransaction[];
  grantedBy: 'admin' | 'referral' | 'signup' | 'cashback' | 'system';
  lastUpdated: any;
  createdAt: any;
}

// Safe date parsing function
const safeParseDate = (timestamp: any): Date => {
  if (!timestamp) return new Date();

  try {
    // If it's a Firestore Timestamp with toDate method
    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }
    // If it's a serialized timestamp with seconds property
    if (timestamp.seconds && typeof timestamp.seconds === 'number') {
      return new Date(timestamp.seconds * 1000);
    }
    // If it's a serialized timestamp with _seconds property
    if (timestamp._seconds && typeof timestamp._seconds === 'number') {
      return new Date(timestamp._seconds * 1000);
    }
    // If it's already a Date object
    if (timestamp instanceof Date) {
      return timestamp;
    }
    // Try to parse as string or number
    const parsed = new Date(timestamp);
    if (isNaN(parsed.getTime())) {
      console.warn('Invalid timestamp, using current date:', timestamp);
      return new Date();
    }
    return parsed;
  } catch (error) {
    console.warn('Error parsing timestamp, using current date:', error, timestamp);
    return new Date();
  }
};

const Wallet: React.FC = () => {
  const { currentUser, isLoading: authLoading } = useAuth();

  const [walletData, setWalletData] = useState<WalletData | null>(null);
  const [isLoadingWallet, setIsLoadingWallet] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to get transaction colors based on type
  const getTransactionColors = (transactionType: string) => {
    const isCredit = transactionType === 'credit';
    return {
      iconBg: isCredit
        ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400'
        : 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
      amountText: isCredit
        ? 'text-green-600 dark:text-green-400'
        : 'text-red-600 dark:text-red-400'
    };
  };
  const [copiedReferral, setCopiedReferral] = useState(false);

  // Load wallet data from Firebase
  const loadWalletData = async () => {
    if (!currentUser) return;

    try {
      setIsLoadingWallet(true);
      setError(null);

      // First try Firebase Functions
      try {
        const getWalletData = httpsCallable(functions, 'getWalletData');
        const result = await getWalletData({});
        const data = result.data as WalletData;

        // Safely parse dates from Firebase Functions response
        const safeData: WalletData = {
          ...data,
          history: (data.history || []).map(transaction => ({
            ...transaction,
            createdAt: safeParseDate(transaction.createdAt),
            expiresAt: transaction.expiresAt ? safeParseDate(transaction.expiresAt) : undefined
          })),
          lastUpdated: safeParseDate(data.lastUpdated),
          createdAt: safeParseDate(data.createdAt)
        };

        setWalletData(safeData);
        console.log('Loaded wallet data from Firebase Functions');
        return;
      } catch (_functionsError) {
        console.log('Firebase Functions not available, using local service');

        // Fallback to local service
        const localWalletData = await LocalWalletService.getWalletData(currentUser.uid);
        const walletData: WalletData = {
          userId: localWalletData.userId,
          balance: localWalletData.balance,
          referralCode: localWalletData.referralCode,
          usedReferral: localWalletData.usedReferral,
          history: localWalletData.history.map(transaction => ({
            ...transaction,
            createdAt: safeParseDate(transaction.createdAt),
            expiresAt: transaction.expiresAt ? safeParseDate(transaction.expiresAt) : undefined
          })),
          grantedBy: localWalletData.grantedBy,
          lastUpdated: safeParseDate(localWalletData.lastUpdated),
          createdAt: safeParseDate(localWalletData.createdAt)
        };
        setWalletData(walletData);
        console.log('Loaded wallet data from local service');
      }
    } catch (error) {
      console.error('Error loading wallet data:', error);
      setError('Failed to load wallet data');
    } finally {
      setIsLoadingWallet(false);
    }
  };

  // Copy referral code to clipboard
  const copyReferralCode = async () => {
    if (!walletData?.referralCode) return;

    try {
      await navigator.clipboard.writeText(walletData.referralCode);
      setCopiedReferral(true);
      setTimeout(() => setCopiedReferral(false), 2000);
    } catch (error) {
      console.error('Failed to copy referral code:', error);
    }
  };

  // Load wallet data on component mount
  useEffect(() => {
    if (!authLoading && currentUser) {
      loadWalletData();
    }
  }, [currentUser, authLoading]);

  // Don't render anything if user is not authenticated
  if (!currentUser && !authLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 text-center">
          <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Authentication Required</h3>
          <p className="text-gray-600 dark:text-gray-400">
            Please log in to view your wallet balance and transaction history.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Wallet Card */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl shadow-lg p-6 text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -mt-12 -mr-12 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/10 rounded-full -mb-8 -ml-8 blur-2xl"></div>
        
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
            <WalletIcon className="w-5 h-5 text-white" />
          </div>
          <h2 className="text-xl font-semibold">Hive Campus Wallet</h2>
        </div>
        
        {(isLoadingWallet || authLoading) ? (
          <div className="flex items-center justify-center py-6">
            <Loader className="w-6 h-6 text-white animate-spin" />
          </div>
        ) : error ? (
          <div className="bg-yellow-500/20 backdrop-blur-md rounded-xl p-4 flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-yellow-300 mt-0.5" />
            <div>
              <p className="font-medium">Wallet System Initializing</p>
              <p className="text-sm text-white/80">
                The wallet system is being set up. Your balance will appear here once it's ready.
              </p>
              <button
                onClick={loadWalletData}
                className="mt-2 text-sm bg-white/20 hover:bg-white/30 px-3 py-1 rounded-lg transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="mb-6">
              <p className="text-sm text-white/80">Available Balance</p>
              <h3 className="text-4xl font-bold mt-1 text-green-300">${walletData?.balance?.toFixed(2) || '0.00'}</h3>
              <p className="text-sm text-white/70 mt-2">
                Non-withdrawable • Use for purchases only
              </p>
              {(walletData?.balance || 0) === 0 && (
                <div className="mt-3 p-3 bg-white/10 rounded-lg">
                  <p className="text-xs text-white/80">
                    💡 <strong>How to earn credits:</strong> Share your referral code with friends or check with admin for promotional bonuses!
                  </p>
                </div>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Gift className="w-5 h-5" />
                  <span className="text-sm font-medium">Referral Code</span>
                </div>
                <div className="flex items-center space-x-2">
                  <code className="text-lg font-mono bg-white/20 px-2 py-1 rounded">
                    {walletData?.referralCode || 'Loading...'}
                  </code>
                  <button
                    onClick={copyReferralCode}
                    className="p-1 hover:bg-white/20 rounded transition-colors"
                    title="Copy referral code"
                  >
                    {copiedReferral ? (
                      <CheckCircle className="w-4 h-4 text-green-300" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="w-5 h-5" />
                  <span className="text-sm font-medium">Earn More</span>
                </div>
                <p className="text-xs text-white/80">
                  Share your code & earn $5 for each friend who joins!
                </p>
              </div>
            </div>
          </>
        )}
      </div>
      
      {/* Transaction History */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Transaction History</h3>
        
        {!walletData?.history || walletData.history.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
              <CreditCard className="w-8 h-8 text-gray-400" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Transactions Yet</h4>
            <p className="text-gray-500 dark:text-gray-400">
              Your transaction history will appear here once you start using your wallet.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {walletData.history.slice().reverse().map((transaction) => {
              const colors = getTransactionColors(transaction.type);

              return (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 border border-gray-100 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colors.iconBg}`}>
                    {transaction.type === 'credit' ? (
                      <ArrowDownLeft className="w-5 h-5" />
                    ) : (
                      <ArrowUpRight className="w-5 h-5" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white flex items-center space-x-2">
                      <span>
                        {transaction.type === 'credit' ? (
                          transaction.source === 'signup_bonus' ? 'Signup Bonus' :
                          transaction.source === 'referral_bonus' ? 'Referral Bonus' :
                          transaction.source === 'admin_grant' ? 'Admin Grant' :
                          transaction.source === 'cashback' ? 'Cashback' : 'Credit'
                        ) : 'Purchase'}
                      </span>
                      {transaction.isExpired && (
                        <span className="text-xs bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 px-2 py-1 rounded">
                          Expired
                        </span>
                      )}
                      {transaction.expiresAt && !transaction.isExpired && transaction.type === 'credit' && (
                        <span className="text-xs bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 px-2 py-1 rounded">
                          Expires {formatTimestamp(transaction.expiresAt)}
                        </span>
                      )}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {transaction.description}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-semibold ${colors.amountText}`}>
                    {transaction.type === 'credit' ? '+' : '-'}${transaction.amount.toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatRelativeTime(transaction.createdAt)}
                  </p>
                </div>
              </div>
              );
            })}
          </div>
        )}
      </div>
      
      {/* Wallet Info */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">About Hive Credit</h3>
        <div className="space-y-4 text-gray-600 dark:text-gray-400">
          <p>
            Hive Credit is our promotional credit system that lets you earn and spend credits within the platform.
          </p>
          <p>
            <span className="font-medium text-gray-900 dark:text-white">Earning Credits:</span> Get $5 for signing up, $5 for each successful referral, plus cashback on purchases.
          </p>
          <p>
            <span className="font-medium text-gray-900 dark:text-white">Using Credits:</span> Apply your credit balance during checkout to reduce the amount charged to your payment method.
          </p>
          <p>
            <span className="font-medium text-gray-900 dark:text-white">Important:</span> Credits are non-withdrawable and can only be used for purchases on Hive Campus.
          </p>
          <p>
            <span className="font-medium text-gray-900 dark:text-white">Referrals:</span> Share your referral code with friends - you both earn $5 when they join!
          </p>
        </div>
      </div>
    </div>
  );
};

export default Wallet;