/**
 * 🚢 SHIPPO API MOCK
 * Mock Shippo shipping API for testing
 */

export class ShippoMock {
  static mockResponses = {
    // Mock address validation response
    validateAddress: {
      object_state: 'VALID',
      object_id: 'test_address_123',
      name: 'Test User',
      street1: '123 Test St',
      city: 'Starkville',
      state: 'MS',
      zip: '39759',
      country: 'US',
      validation_results: {
        is_valid: true
      }
    },

    // Mock shipment creation response
    createShipment: {
      object_state: 'VALID',
      object_id: 'test_shipment_123',
      address_from: 'test_address_from_123',
      address_to: 'test_address_to_123',
      parcels: ['test_parcel_123'],
      rates: [
        {
          object_id: 'test_rate_123',
          amount: '7.50',
          currency: 'USD',
          amount_local: '7.50',
          currency_local: 'USD',
          provider: 'USPS',
          servicelevel: {
            name: 'Priority Mail',
            token: 'usps_priority'
          },
          estimated_days: 2,
          duration_terms: 'Delivery in 1-3 business days'
        },
        {
          object_id: 'test_rate_124',
          amount: '5.25',
          currency: 'USD',
          amount_local: '5.25',
          currency_local: 'USD',
          provider: 'USPS',
          servicelevel: {
            name: 'Ground Advantage',
            token: 'usps_ground_advantage'
          },
          estimated_days: 5,
          duration_terms: 'Delivery in 3-5 business days'
        }
      ]
    },

    // Mock transaction (label purchase) response
    createTransaction: {
      object_state: 'SUCCESS',
      object_id: 'test_transaction_123',
      rate: 'test_rate_123',
      tracking_number: '1Z999AA1234567890',
      tracking_status: 'UNKNOWN',
      tracking_url_provider: 'https://tools.usps.com/go/TrackConfirmAction?tLabels=1Z999AA1234567890',
      label_url: 'https://shippo-delivery.s3.amazonaws.com/test-label.pdf',
      commercial_invoice_url: null,
      metadata: 'Test shipment for Hive Campus',
      messages: [],
      carrier_account: 'test_carrier_account_123',
      test: true
    },

    // Mock tracking response
    getTracking: {
      carrier: 'usps',
      tracking_number: '1Z999AA1234567890',
      address_from: {
        city: 'Starkville',
        state: 'MS',
        zip: '39759',
        country: 'US'
      },
      address_to: {
        city: 'Starkville',
        state: 'MS',
        zip: '39759',
        country: 'US'
      },
      eta: '2024-01-15T17:00:00Z',
      original_eta: '2024-01-15T17:00:00Z',
      servicelevel: {
        token: 'usps_priority',
        name: 'Priority Mail'
      },
      tracking_status: {
        object_created: '2024-01-10T10:00:00Z',
        object_id: 'test_tracking_123',
        status: 'DELIVERED',
        status_details: 'Package delivered to recipient',
        status_date: '2024-01-15T16:30:00Z',
        location: {
          city: 'Starkville',
          state: 'MS',
          zip: '39759',
          country: 'US'
        }
      },
      tracking_history: [
        {
          object_created: '2024-01-10T10:00:00Z',
          object_id: 'test_history_1',
          status: 'TRANSIT',
          status_details: 'Package in transit',
          status_date: '2024-01-10T10:00:00Z',
          location: {
            city: 'Starkville',
            state: 'MS',
            zip: '39759',
            country: 'US'
          }
        },
        {
          object_created: '2024-01-15T16:30:00Z',
          object_id: 'test_history_2',
          status: 'DELIVERED',
          status_details: 'Package delivered to recipient',
          status_date: '2024-01-15T16:30:00Z',
          location: {
            city: 'Starkville',
            state: 'MS',
            zip: '39759',
            country: 'US'
          }
        }
      ],
      test: true
    }
  };

  /**
   * Setup Shippo API mocks
   */
  static setup() {
    // Mock global fetch for Shippo API calls
    global.fetch = async (url, options) => {
      console.log(`🚢 Mock Shippo API call: ${url}`);
      
      // Parse the URL to determine which endpoint is being called
      if (url.includes('/addresses/')) {
        return {
          ok: true,
          status: 200,
          json: async () => this.mockResponses.validateAddress
        };
      }
      
      if (url.includes('/shipments/')) {
        return {
          ok: true,
          status: 200,
          json: async () => this.mockResponses.createShipment
        };
      }
      
      if (url.includes('/transactions/')) {
        return {
          ok: true,
          status: 200,
          json: async () => this.mockResponses.createTransaction
        };
      }
      
      if (url.includes('/tracks/')) {
        return {
          ok: true,
          status: 200,
          json: async () => this.mockResponses.getTracking
        };
      }
      
      // Default response for unknown endpoints
      return {
        ok: false,
        status: 404,
        json: async () => ({ error: 'Mock endpoint not found' })
      };
    };

    console.log('🚢 Shippo API mocks initialized');
  }

  /**
   * Generate mock shipping rates
   */
  static generateMockRates(fromAddress, toAddress, parcel) {
    const baseRates = [
      {
        provider: 'USPS',
        servicelevel: 'Priority Mail',
        amount: '7.50',
        estimated_days: 2
      },
      {
        provider: 'USPS',
        servicelevel: 'Ground Advantage',
        amount: '5.25',
        estimated_days: 5
      },
      {
        provider: 'UPS',
        servicelevel: 'Ground',
        amount: '8.75',
        estimated_days: 3
      },
      {
        provider: 'FedEx',
        servicelevel: 'Ground',
        amount: '9.25',
        estimated_days: 3
      }
    ];

    return baseRates.map((rate, index) => ({
      object_id: `test_rate_${index + 123}`,
      amount: rate.amount,
      currency: 'USD',
      amount_local: rate.amount,
      currency_local: 'USD',
      provider: rate.provider,
      servicelevel: {
        name: rate.servicelevel,
        token: `${rate.provider.toLowerCase()}_${rate.servicelevel.toLowerCase().replace(' ', '_')}`
      },
      estimated_days: rate.estimated_days,
      duration_terms: `Delivery in ${rate.estimated_days === 2 ? '1-3' : rate.estimated_days === 3 ? '2-4' : '3-5'} business days`
    }));
  }

  /**
   * Generate mock tracking number
   */
  static generateMockTrackingNumber(carrier = 'USPS') {
    const patterns = {
      USPS: () => `9400111899562${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`,
      UPS: () => `1Z999AA1${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      FedEx: () => `${Math.floor(Math.random() * 10000000000000).toString().padStart(12, '0')}`
    };

    return patterns[carrier] ? patterns[carrier]() : patterns.USPS();
  }

  /**
   * Generate mock shipping label
   */
  static generateMockLabel(rateId, trackingNumber = null) {
    const generatedTrackingNumber = trackingNumber || this.generateMockTrackingNumber();
    
    return {
      object_state: 'SUCCESS',
      object_id: `test_transaction_${Math.random().toString(36).substring(2, 15)}`,
      rate: rateId,
      tracking_number: generatedTrackingNumber,
      tracking_status: 'UNKNOWN',
      tracking_url_provider: `https://tools.usps.com/go/TrackConfirmAction?tLabels=${generatedTrackingNumber}`,
      label_url: `https://shippo-delivery.s3.amazonaws.com/test-label-${generatedTrackingNumber}.pdf`,
      commercial_invoice_url: null,
      metadata: 'Test shipment for Hive Campus marketplace',
      messages: [],
      carrier_account: 'test_carrier_account_123',
      test: true,
      carrier: 'USPS'
    };
  }

  /**
   * Generate mock return label
   */
  static generateMockReturnLabel(originalTrackingNumber) {
    const returnTrackingNumber = this.generateMockTrackingNumber();
    
    return {
      object_state: 'SUCCESS',
      object_id: `test_return_transaction_${Math.random().toString(36).substring(2, 15)}`,
      tracking_number: returnTrackingNumber,
      tracking_status: 'UNKNOWN',
      tracking_url_provider: `https://tools.usps.com/go/TrackConfirmAction?tLabels=${returnTrackingNumber}`,
      label_url: `https://shippo-delivery.s3.amazonaws.com/test-return-label-${returnTrackingNumber}.pdf`,
      commercial_invoice_url: null,
      metadata: `Return label for original shipment: ${originalTrackingNumber}`,
      messages: [],
      carrier_account: 'test_carrier_account_123',
      test: true,
      carrier: 'USPS',
      is_return: true
    };
  }

  /**
   * Cleanup mocks
   */
  static cleanup() {
    // Restore original fetch if it was mocked
    if (global.fetch && global.fetch.isMock) {
      delete global.fetch;
    }
    
    console.log('🚢 Shippo API mocks cleaned up');
  }

  /**
   * Simulate webhook from Shippo for tracking updates
   */
  static simulateTrackingWebhook(trackingNumber, status = 'DELIVERED') {
    return {
      event: 'track_updated',
      test: true,
      data: {
        carrier: 'usps',
        tracking_number: trackingNumber,
        tracking_status: {
          status: status,
          status_details: status === 'DELIVERED' ? 'Package delivered to recipient' : 'Package in transit',
          status_date: new Date().toISOString(),
          location: {
            city: 'Starkville',
            state: 'MS',
            zip: '39759',
            country: 'US'
          }
        }
      }
    };
  }
}
