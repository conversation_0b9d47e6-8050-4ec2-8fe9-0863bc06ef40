# Production Environment Variables for Hive Campus
# Copy this file to .env.production and fill in your actual production values
# NEVER commit .env.production to version control

# Firebase Configuration (Production)
VITE_FIREBASE_API_KEY=your_production_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your-production-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-production-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-production-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_production_sender_id
VITE_FIREBASE_APP_ID=your_production_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_production_measurement_id

# Stripe Configuration (Production)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_production_stripe_publishable_key

# Analytics and Monitoring (Production)
# Get Sentry DSN from Sentry.io project settings
VITE_SENTRY_DSN=https://<EMAIL>/project_id

# ReeFlex AI Moderation (Production)
# Get from ReeFlex dashboard
VITE_REEFLEX_PROJECT_ID=your_production_reeflex_project_id

# App Configuration
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Security Configuration
# Set to true in production to enforce HTTPS
VITE_ENFORCE_HTTPS=true

# Feature Flags (Production)
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
