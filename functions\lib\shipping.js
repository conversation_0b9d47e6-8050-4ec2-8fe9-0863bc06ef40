"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecureShippingManager = void 0;
const admin = __importStar(require("firebase-admin"));
const functions = __importStar(require("firebase-functions/v1"));
const schemas_1 = require("./validation/schemas");
const zod_1 = require("zod");
const axios_1 = __importDefault(require("axios"));
// Secure shipping system with Shippo API integration and rate limiting
// Shipping label request schema
const ShippingLabelSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1, 'Order ID is required'),
    fromAddress: zod_1.z.object({
        name: zod_1.z.string().min(1).max(50),
        street1: zod_1.z.string().min(1).max(100),
        city: zod_1.z.string().min(1).max(50),
        state: zod_1.z.string().length(2),
        zip: zod_1.z.string().regex(/^\d{5}(-\d{4})?$/),
        country: zod_1.z.string().length(2).default('US')
    }),
    toAddress: zod_1.z.object({
        name: zod_1.z.string().min(1).max(50),
        street1: zod_1.z.string().min(1).max(100),
        city: zod_1.z.string().min(1).max(50),
        state: zod_1.z.string().length(2),
        zip: zod_1.z.string().regex(/^\d{5}(-\d{4})?$/),
        country: zod_1.z.string().length(2).default('US')
    }),
    parcel: zod_1.z.object({
        length: zod_1.z.number().positive().max(108), // inches
        width: zod_1.z.number().positive().max(108),
        height: zod_1.z.number().positive().max(108),
        weight: zod_1.z.number().positive().max(150), // pounds
        distance_unit: zod_1.z.enum(['in', 'cm']).default('in'),
        mass_unit: zod_1.z.enum(['lb', 'kg']).default('lb')
    }),
    serviceLevel: zod_1.z.enum(['ground', 'priority', 'express']).default('ground')
});
// Tracking update schema
const TrackingUpdateSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1, 'Order ID is required'),
    trackingNumber: zod_1.z.string().min(1, 'Tracking number is required'),
    carrier: zod_1.z.string().min(1, 'Carrier is required')
});
/**
 * Secure shipping manager with rate limiting and API security
 */
class SecureShippingManager {
    /**
     * Generate shipping label with rate limiting
     */
    static async generateShippingLabel(sellerId, labelData) {
        try {
            // Validate input
            const validatedData = (0, schemas_1.validateInput)(ShippingLabelSchema, labelData);
            // Check rate limiting
            const canGenerate = await this.checkLabelRateLimit(sellerId);
            if (!canGenerate) {
                throw new functions.https.HttpsError('resource-exhausted', 'Label generation rate limit exceeded. Maximum 10 labels per hour.');
            }
            // Verify order ownership
            const orderDoc = await admin.firestore()
                .collection('orders')
                .doc(validatedData.orderId)
                .get();
            if (!orderDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'Order not found');
            }
            const orderData = orderDoc.data();
            if ((orderData === null || orderData === void 0 ? void 0 : orderData.sellerId) !== sellerId) {
                throw new functions.https.HttpsError('permission-denied', 'Not authorized to generate label for this order');
            }
            // Check if label already exists
            if (orderData === null || orderData === void 0 ? void 0 : orderData.shippingLabel) {
                return {
                    success: true,
                    label: orderData.shippingLabel
                };
            }
            // Generate label via Shippo API
            const label = await this.callShippoAPI(validatedData);
            // Store label in order
            await admin.firestore()
                .collection('orders')
                .doc(validatedData.orderId)
                .update({
                shippingLabel: label,
                trackingNumber: label.trackingNumber,
                carrier: label.carrier,
                labelGeneratedAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
            });
            // Log label generation
            await this.logLabelGeneration(sellerId, validatedData.orderId, label);
            console.log(`📦 Shipping label generated for order ${validatedData.orderId} by seller ${sellerId}`);
            return { success: true, label };
        }
        catch (error) {
            console.error('❌ Error generating shipping label:', error);
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            return {
                success: false,
                error: 'Failed to generate shipping label'
            };
        }
    }
    /**
     * Update tracking information
     */
    static async updateTracking(orderId, trackingData) {
        try {
            // Validate input
            const validatedData = (0, schemas_1.validateInput)(TrackingUpdateSchema, Object.assign({ orderId }, trackingData));
            // Get tracking info from Shippo
            const trackingInfo = await this.getTrackingInfo(validatedData.trackingNumber, validatedData.carrier);
            // Update order with tracking info
            await admin.firestore()
                .collection('orders')
                .doc(orderId)
                .update({
                trackingInfo,
                lastTrackingUpdate: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
            });
            // Check if delivered and update status
            if (trackingInfo.status === 'DELIVERED') {
                // Import here to avoid circular dependency
                const { SecureOrderStatusManager, OrderStatus } = await Promise.resolve().then(() => __importStar(require('./order-status')));
                await SecureOrderStatusManager.updateOrderStatus(orderId, OrderStatus.DELIVERED, 'system', {
                    reason: 'Package delivered according to tracking',
                    proofOfDelivery: {
                        type: 'tracking_update',
                        timestamp: trackingInfo.timestamp,
                        metadata: {
                            carrier: validatedData.carrier,
                            trackingNumber: validatedData.trackingNumber,
                            location: trackingInfo.location
                        }
                    },
                    isSystemUpdate: true
                });
            }
            console.log(`📍 Tracking updated for order ${orderId}: ${trackingInfo.status}`);
            return { success: true, trackingInfo };
        }
        catch (error) {
            console.error('❌ Error updating tracking:', error);
            return { success: false };
        }
    }
    /**
     * Get return label for disputes
     */
    static async generateReturnLabel(orderId, adminId) {
        var _a;
        try {
            // Verify admin authorization
            const adminDoc = await admin.firestore()
                .collection('users')
                .doc(adminId)
                .get();
            if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
                throw new functions.https.HttpsError('permission-denied', 'Only admins can generate return labels');
            }
            // Get order data
            const orderDoc = await admin.firestore()
                .collection('orders')
                .doc(orderId)
                .get();
            if (!orderDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'Order not found');
            }
            const orderData = orderDoc.data();
            // Check if return label already exists
            if (orderData === null || orderData === void 0 ? void 0 : orderData.returnLabel) {
                return {
                    success: true,
                    label: orderData.returnLabel
                };
            }
            // Generate return label (reverse addresses)
            const returnLabelData = {
                orderId,
                fromAddress: orderData.shippingAddress, // Buyer's address
                toAddress: orderData.sellerAddress, // Seller's address
                parcel: orderData.parcel || {
                    length: 12,
                    width: 9,
                    height: 6,
                    weight: 1,
                    distance_unit: 'in',
                    mass_unit: 'lb'
                },
                serviceLevel: 'ground'
            };
            const returnLabel = await this.callShippoAPI(returnLabelData);
            // Store return label
            await admin.firestore()
                .collection('orders')
                .doc(orderId)
                .update({
                returnLabel,
                returnLabelGeneratedAt: admin.firestore.Timestamp.now(),
                returnLabelGeneratedBy: adminId,
                updatedAt: admin.firestore.Timestamp.now()
            });
            console.log(`🔄 Return label generated for order ${orderId} by admin ${adminId}`);
            return { success: true, label: returnLabel };
        }
        catch (error) {
            console.error('❌ Error generating return label:', error);
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            return { success: false };
        }
    }
    /**
     * Check label generation rate limit
     */
    static async checkLabelRateLimit(sellerId) {
        try {
            const rateLimitRef = admin.firestore()
                .collection('rateLimits')
                .doc(`shipping_${sellerId}`);
            const result = await admin.firestore().runTransaction(async (transaction) => {
                var _a;
                const doc = await transaction.get(rateLimitRef);
                const now = admin.firestore.Timestamp.now();
                const windowStart = now.toMillis() - this.RATE_LIMIT_WINDOW;
                if (!doc.exists) {
                    transaction.set(rateLimitRef, {
                        count: 1,
                        windowStart: now,
                        lastRequest: now
                    });
                    return true;
                }
                const data = doc.data();
                const lastWindowStart = ((_a = data === null || data === void 0 ? void 0 : data.windowStart) === null || _a === void 0 ? void 0 : _a.toMillis()) || 0;
                // Reset if window has passed
                if (lastWindowStart < windowStart) {
                    transaction.set(rateLimitRef, {
                        count: 1,
                        windowStart: now,
                        lastRequest: now
                    });
                    return true;
                }
                // Check if limit exceeded
                if ((data === null || data === void 0 ? void 0 : data.count) >= this.MAX_LABELS_PER_HOUR) {
                    return false;
                }
                // Increment count
                transaction.update(rateLimitRef, {
                    count: admin.firestore.FieldValue.increment(1),
                    lastRequest: now
                });
                return true;
            });
            return result;
        }
        catch (error) {
            console.error('❌ Error checking label rate limit:', error);
            return false; // Fail secure
        }
    }
    /**
     * Call Shippo API securely
     */
    static async callShippoAPI(labelData) {
        try {
            if (!this.API_KEY) {
                throw new Error('Shippo API key not configured');
            }
            // Create shipment
            const shipmentResponse = await axios_1.default.post(`${this.SHIPPO_API_URL}/shipments/`, {
                address_from: labelData.fromAddress,
                address_to: labelData.toAddress,
                parcels: [labelData.parcel],
                async: false
            }, {
                headers: {
                    'Authorization': `ShippoToken ${this.API_KEY}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000 // 30 second timeout
            });
            const shipment = shipmentResponse.data;
            // Find the best rate
            const rates = shipment.rates || [];
            const selectedRate = rates.find((rate) => rate.servicelevel.token === labelData.serviceLevel) || rates[0];
            if (!selectedRate) {
                throw new Error('No shipping rates available');
            }
            // Create transaction (purchase label)
            const transactionResponse = await axios_1.default.post(`${this.SHIPPO_API_URL}/transactions/`, {
                rate: selectedRate.object_id,
                label_file_type: 'PDF',
                async: false
            }, {
                headers: {
                    'Authorization': `ShippoToken ${this.API_KEY}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });
            const transaction = transactionResponse.data;
            if (transaction.status !== 'SUCCESS') {
                throw new Error(`Label generation failed: ${transaction.messages}`);
            }
            return {
                labelUrl: transaction.label_url,
                trackingNumber: transaction.tracking_number,
                carrier: selectedRate.provider,
                service: selectedRate.servicelevel.name,
                cost: parseFloat(selectedRate.amount),
                estimatedDelivery: selectedRate.estimated_days
            };
        }
        catch (error) {
            console.error('❌ Shippo API call failed:', error);
            throw new functions.https.HttpsError('internal', 'Failed to generate shipping label');
        }
    }
    /**
     * Get tracking information from Shippo
     */
    static async getTrackingInfo(trackingNumber, carrier) {
        var _a, _b, _c, _d;
        try {
            if (!this.API_KEY) {
                throw new Error('Shippo API key not configured');
            }
            const response = await axios_1.default.get(`${this.SHIPPO_API_URL}/tracks/${carrier}/${trackingNumber}/`, {
                headers: {
                    'Authorization': `ShippoToken ${this.API_KEY}`
                },
                timeout: 15000
            });
            const tracking = response.data;
            return {
                status: ((_a = tracking.tracking_status) === null || _a === void 0 ? void 0 : _a.status) || 'UNKNOWN',
                location: ((_c = (_b = tracking.tracking_status) === null || _b === void 0 ? void 0 : _b.location) === null || _c === void 0 ? void 0 : _c.city) || 'Unknown',
                timestamp: ((_d = tracking.tracking_status) === null || _d === void 0 ? void 0 : _d.status_date) || new Date().toISOString(),
                estimatedDelivery: tracking.eta,
                events: (tracking.tracking_history || []).map((event) => {
                    var _a;
                    return ({
                        status: event.status,
                        location: ((_a = event.location) === null || _a === void 0 ? void 0 : _a.city) || 'Unknown',
                        timestamp: event.status_date,
                        description: event.status_details
                    });
                })
            };
        }
        catch (error) {
            console.error('❌ Error getting tracking info:', error);
            // Return basic info if API fails
            return {
                status: 'UNKNOWN',
                location: 'Unknown',
                timestamp: new Date().toISOString(),
                events: []
            };
        }
    }
    /**
     * Log label generation for audit trail
     */
    static async logLabelGeneration(sellerId, orderId, label) {
        try {
            await admin.firestore().collection('shippingLogs').add({
                sellerId,
                orderId,
                action: 'label_generated',
                labelInfo: {
                    trackingNumber: label.trackingNumber,
                    carrier: label.carrier,
                    cost: label.cost
                },
                timestamp: admin.firestore.Timestamp.now()
            });
        }
        catch (error) {
            console.error('❌ Error logging label generation:', error);
        }
    }
    /**
     * Validate shipping address
     */
    static validateAddress(address) {
        try {
            const schema = zod_1.z.object({
                name: zod_1.z.string().min(1).max(50),
                street1: zod_1.z.string().min(1).max(100),
                city: zod_1.z.string().min(1).max(50),
                state: zod_1.z.string().length(2),
                zip: zod_1.z.string().regex(/^\d{5}(-\d{4})?$/),
                country: zod_1.z.string().length(2)
            });
            schema.parse(address);
            return true;
        }
        catch (_a) {
            return false;
        }
    }
}
exports.SecureShippingManager = SecureShippingManager;
SecureShippingManager.SHIPPO_API_URL = 'https://api.goshippo.com/v1';
SecureShippingManager.API_KEY = ((_a = functions.config().shippo) === null || _a === void 0 ? void 0 : _a.api_key) || process.env.SHIPPO_API_KEY;
SecureShippingManager.RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour
SecureShippingManager.MAX_LABELS_PER_HOUR = 10;
