"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSecureStripeInstance = exports.getWebhookSecret = exports.SecureStripeWebhook = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const stripe_1 = __importDefault(require("stripe"));
// Secure Stripe webhook utilities for Hive Campus
/**
 * Secure Stripe webhook handler with signature verification
 */
class SecureStripeWebhook {
    /**
     * Verify Stripe webhook signature and construct event
     */
    static verifyWebhookSignature(rawBody, signature, endpointSecret) {
        try {
            // Verify the webhook signature
            const event = this.stripe.webhooks.constructEvent(rawBody, signature, endpointSecret);
            console.log(`✅ Webhook signature verified for event: ${event.type}`);
            return event;
        }
        catch (error) {
            console.error('❌ Webhook signature verification failed:', error);
            if (error instanceof Error) {
                if (error.message.includes('timestamp')) {
                    throw new functions.https.HttpsError('invalid-argument', 'Webhook timestamp too old');
                }
                else if (error.message.includes('signature')) {
                    throw new functions.https.HttpsError('unauthenticated', 'Invalid webhook signature');
                }
            }
            throw new functions.https.HttpsError('unauthenticated', 'Webhook verification failed');
        }
    }
    /**
     * Validate webhook event type and structure
     */
    static validateWebhookEvent(event) {
        // List of allowed event types for security
        const allowedEventTypes = [
            'checkout.session.completed',
            'payment_intent.succeeded',
            'payment_intent.payment_failed',
            'account.updated',
            'invoice.payment_succeeded',
            'invoice.payment_failed'
        ];
        if (!allowedEventTypes.includes(event.type)) {
            console.warn(`⚠️ Received unexpected webhook event type: ${event.type}`);
            return false;
        }
        // Validate event structure
        if (!event.id || !event.created || !event.data) {
            console.error('❌ Invalid webhook event structure');
            return false;
        }
        // Check event age (reject events older than 5 minutes for security)
        const eventAge = Date.now() / 1000 - event.created;
        if (eventAge > 300) { // 5 minutes
            console.warn(`⚠️ Webhook event too old: ${eventAge} seconds`);
            return false;
        }
        return true;
    }
    /**
     * Extract and validate metadata from Stripe objects
     */
    static validateMetadata(stripeObject) {
        const metadata = stripeObject.metadata || {};
        // Validate required metadata fields
        const requiredFields = ['orderId', 'buyerId', 'sellerId'];
        const missingFields = requiredFields.filter(field => !metadata[field]);
        if (missingFields.length > 0) {
            throw new functions.https.HttpsError('invalid-argument', `Missing required metadata fields: ${missingFields.join(', ')}`);
        }
        // Validate metadata values
        Object.entries(metadata).forEach(([key, value]) => {
            if (typeof value !== 'string' || value.length === 0) {
                throw new functions.https.HttpsError('invalid-argument', `Invalid metadata value for ${key}`);
            }
            // Sanitize metadata values
            if (value.length > 500) {
                throw new functions.https.HttpsError('invalid-argument', `Metadata value too long for ${key}`);
            }
        });
        return metadata;
    }
    /**
     * Validate payment amounts for security
     */
    static validatePaymentAmount(amount, expectedAmount) {
        // Check for reasonable amount limits
        if (amount < 50 || amount > 1000000) { // $0.50 to $10,000
            console.error(`❌ Payment amount outside allowed range: $${amount / 100}`);
            return false;
        }
        // If expected amount is provided, verify it matches
        if (expectedAmount !== undefined) {
            const tolerance = 1; // 1 cent tolerance
            if (Math.abs(amount - expectedAmount) > tolerance) {
                console.error(`❌ Payment amount mismatch: expected $${expectedAmount / 100}, got $${amount / 100}`);
                return false;
            }
        }
        return true;
    }
    /**
     * Log webhook events for security monitoring
     */
    static logWebhookEvent(event, status, error) {
        const logData = {
            eventId: event.id,
            eventType: event.type,
            status,
            timestamp: new Date().toISOString(),
            error: error || null,
            metadata: event.data.object.metadata || {}
        };
        console.log(`📊 Webhook Event Log:`, JSON.stringify(logData, null, 2));
        // In production, you might want to store this in Firestore for audit trails
        // await admin.firestore().collection('webhookLogs').add(logData);
    }
    /**
     * Handle webhook processing with error recovery
     */
    static async processWebhookSafely(event, processor) {
        try {
            const result = await processor(event);
            this.logWebhookEvent(event, 'success');
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logWebhookEvent(event, 'error', errorMessage);
            // Re-throw the error for proper HTTP response
            throw error;
        }
    }
    /**
     * Validate Stripe Connect account for security
     */
    static validateConnectAccount(account) {
        // Check if account is properly onboarded
        if (!account.details_submitted || !account.charges_enabled) {
            console.warn(`⚠️ Connect account not fully onboarded: ${account.id}`);
            return false;
        }
        // Check for suspicious account patterns
        if (account.created && Date.now() / 1000 - account.created < 3600) {
            console.warn(`⚠️ Very new Connect account: ${account.id}`);
            // Don't reject, but log for monitoring
        }
        return true;
    }
    /**
     * Create secure checkout session with validation
     */
    static async createSecureCheckoutSession(params) {
        // Validate parameters
        if (!this.validatePaymentAmount(params.amount)) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid payment amount');
        }
        // Validate URLs
        const allowedDomains = [
            'https://h1c1-798a8.web.app',
            'https://h1c1-798a8.firebaseapp.com',
            'https://hivecampus.app',
            'https://www.hivecampus.app'
        ];
        const isValidUrl = (url) => allowedDomains.some(domain => url.startsWith(domain));
        if (!isValidUrl(params.successUrl) || !isValidUrl(params.cancelUrl)) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid redirect URLs');
        }
        try {
            const session = await this.stripe.checkout.sessions.create({
                payment_method_types: ['card'],
                line_items: [{
                        price_data: {
                            currency: params.currency,
                            product_data: {
                                name: 'Hive Campus Purchase'
                            },
                            unit_amount: params.amount
                        },
                        quantity: 1
                    }],
                mode: 'payment',
                success_url: params.successUrl,
                cancel_url: params.cancelUrl,
                metadata: params.metadata,
                // Security settings
                payment_intent_data: {
                    metadata: params.metadata
                },
                expires_at: Math.floor(Date.now() / 1000) + (30 * 60) // 30 minutes
            });
            console.log(`✅ Secure checkout session created: ${session.id}`);
            return session;
        }
        catch (error) {
            console.error('❌ Error creating checkout session:', error);
            throw new functions.https.HttpsError('internal', 'Failed to create checkout session');
        }
    }
}
exports.SecureStripeWebhook = SecureStripeWebhook;
SecureStripeWebhook.stripe = new stripe_1.default(((_a = functions.config().stripe) === null || _a === void 0 ? void 0 : _a.api_key) || process.env.STRIPE_API_KEY || '', { apiVersion: '2025-05-28.basil' });
// Export webhook endpoint secret getter
const getWebhookSecret = () => {
    var _a;
    const secret = ((_a = functions.config().stripe) === null || _a === void 0 ? void 0 : _a.webhook_secret) || process.env.STRIPE_WEBHOOK_SECRET;
    if (!secret) {
        throw new functions.https.HttpsError('failed-precondition', 'Stripe webhook secret not configured');
    }
    return secret;
};
exports.getWebhookSecret = getWebhookSecret;
// Export Stripe instance for other uses
const getSecureStripeInstance = () => {
    var _a;
    const apiKey = ((_a = functions.config().stripe) === null || _a === void 0 ? void 0 : _a.api_key) || process.env.STRIPE_API_KEY;
    if (!apiKey) {
        throw new functions.https.HttpsError('failed-precondition', 'Stripe API key not configured');
    }
    return new stripe_1.default(apiKey, { apiVersion: '2025-05-28.basil' });
};
exports.getSecureStripeInstance = getSecureStripeInstance;
