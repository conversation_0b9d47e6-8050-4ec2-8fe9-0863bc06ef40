{"timestamp": "2025-07-22T19:26:00.489Z", "overallScore": 81, "passed": 26, "failed": 3, "warnings": 3, "categories": {"firebase-rules": {"tests": [{"test": "No unsafe global read/write rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:00.682Z"}, {"test": "Authentication checks implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:00.682Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:00.682Z"}, {"test": "User ownership validation present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:00.682Z"}, {"test": "Input validation in security rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:00.683Z"}], "score": 100}, "secrets": {"tests": [{"test": "No hardcoded secrets in source code", "status": "FAIL", "severity": "Critical", "details": null, "timestamp": "2025-07-22T19:26:00.783Z"}, {"test": "All required environment variables present", "status": "FAIL", "severity": "Critical", "details": "Missing: VITE_FIREBASE_API_KEY, VITE_FIREBASE_AUTH_DOMAIN, VITE_FIREBASE_PROJECT_ID, VITE_FIREBASE_STORAGE_BUCKET, VITE_FIREBASE_MESSAGING_SENDER_ID, VITE_FIREBASE_APP_ID, VITE_STRIPE_PUBLISHABLE_KEY", "timestamp": "2025-07-22T19:26:00.829Z"}, {"test": "No .env files committed to repository", "status": "FAIL", "severity": "High", "details": "Found: .env, .env.production", "timestamp": "2025-07-22T19:26:00.830Z"}], "score": 0}, "dependencies": {"tests": [{"test": "No critical vulnerabilities", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:02.401Z"}, {"test": "No high-severity vulnerabilities", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:02.401Z"}, {"test": "Moderate vulnerabilities under control", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:02.402Z"}, {"test": "Some dependencies may be outdated", "status": "WARN", "severity": "Low", "details": null, "timestamp": "2025-07-22T19:26:05.205Z"}], "score": 75}, "client-security": {"tests": [{"test": "Security utilities implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.206Z"}, {"test": "Input sanitization implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.206Z"}, {"test": "HTTPS enforcement implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.206Z"}, {"test": "Client-side rate limiting implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.206Z"}, {"test": "Security initialization in main app", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.207Z"}], "score": 100}, "network-security": {"tests": [{"test": "Security headers configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:26:05.208Z"}, {"test": "HTTPS configuration present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.208Z"}, {"test": "Firebase hosting security headers configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.236Z"}, {"test": "HSTS header configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:26:05.236Z"}], "score": 50}, "data-privacy": {"tests": [{"test": "Privacy policy page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.237Z"}, {"test": "Terms and conditions page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.237Z"}, {"test": "Consent management implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.508Z"}, {"test": "Data deletion policies in Firebase rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.509Z"}], "score": 100}, "csp": {"tests": [{"test": "CSP manager implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.509Z"}, {"test": "Nonce generation for inline scripts", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.510Z"}, {"test": "CSP meta tag in HTML", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.510Z"}], "score": 100}, "authentication": {"tests": [{"test": "Authentication context implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.510Z"}, {"test": "Protected route component implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.510Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.511Z"}, {"test": "Firebase Auth domain configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:26:05.511Z"}], "score": 100}}}