rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isEmailVerified() {
      return isAuthenticated() && request.auth.token.email_verified == true;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }

    function isAdmin() {
      return hasRole('admin') || (isAuthenticated() && request.auth.token.role == 'admin');
    }
    
    function isMerchant() {
      return hasRole('merchant');
    }
    
    function isStudent() {
      return hasRole('student');
    }
    
    // Users collection
    match /users/{userId} {
      // SECURITY FIX: Allow reading basic profile info for authenticated users
      // This is needed for chat functionality and order management
      allow read: if isAuthenticated() && (
        request.auth.uid == userId ||
        isAdmin() ||
        // Allow reading basic profile info (name, profilePictureURL) for chat functionality
        // This is necessary for displaying user info in chats and order management
        isAuthenticated()
      );
      allow create: if isAuthenticated() && request.auth.uid == userId;
      allow update: if isAuthenticated() && (
        isOwner(userId) ||
        isAdmin() ||
        // Allow users to update their own FCM token and related fields
        (request.auth.uid == userId &&
         request.resource.data.diff(resource.data).affectedKeys().hasOnly(['fcmToken', 'fcmTokenUpdatedAt', 'fcmTokenPlatform', 'fcmTokenUserAgent']))
      );
      allow delete: if isAdmin();

      // User addresses subcollection
      match /addresses/{addressId} {
        // Only the user can read, create, update, or delete their own addresses
        allow read, write: if isAuthenticated() && request.auth.uid == userId;
      }

      // User seller addresses subcollection
      match /sellerAddresses/{addressId} {
        // Only the user can read, create, update, or delete their own seller addresses
        allow read, write: if isAuthenticated() && request.auth.uid == userId;
      }

      // User notifications subcollection
      match /notifications/{notificationId} {
        // Only the user can read, update, or delete their own notifications
        allow read, update, delete: if isAuthenticated() && request.auth.uid == userId;
        // System (admin) can create notifications for users
        allow create: if isAdmin();
      }

      // User FCM tokens subcollection
      match /fcmTokens/{tokenId} {
        // Only the user can read, create, update, or delete their own FCM tokens
        allow read, write: if isAuthenticated() && request.auth.uid == userId;
      }
    }

    // Wallets collection
    match /wallets/{userId} {
      // Users can only read their own wallet balance
      allow read: if isEmailVerified() && request.auth.uid == userId;
      // Only Cloud Functions (admin) can write to wallet balance
      // Users cannot directly modify their wallet balance
      allow write: if isAdmin();
    }

    // Referral codes collection
    match /referralCodes/{codeId} {
      // Anyone can read referral codes to validate them
      allow read: if isAuthenticated();
      // Only the system can create/update referral codes
      allow write: if isAdmin();
    }

    // Listings collection
    match /listings/{listingId} {
      // Anyone can read active listings
      // Only the owner can create, update, or delete their listings
      allow read: if resource.data.status != 'deleted' || isOwner(resource.data.ownerId) || isAdmin();
      allow create: if isAuthenticated() && request.resource.data.ownerId == request.auth.uid;
      allow update: if isOwner(resource.data.ownerId) || isAdmin();
      allow delete: if isOwner(resource.data.ownerId) || isAdmin();
    }
    
    // Chats collection
    match /chats/{chatId} {
      // Allow reading if user is a participant OR if user is an admin (for moderation)
      allow read: if isAuthenticated() &&
        (isAdmin() || resource == null || request.auth.uid in resource.data.participants);

      // Allow creating a chat if the user is one of the participants
      allow create: if isAuthenticated() &&
        request.auth.uid in request.resource.data.participants;

      // Allow updating if user is a participant (for lastMessage updates) OR if user is an admin
      allow update: if isAuthenticated() &&
        (isAdmin() || request.auth.uid in resource.data.participants);

      // Allow deleting if user is an admin (for moderation)
      allow delete: if isAdmin();

      // Messages subcollection
      match /messages/{messageId} {
        // Allow reading messages if user is a participant in the chat OR if user is an admin
        allow read: if isAuthenticated() &&
          (isAdmin() || request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants);

        // Allow creating messages if user is a participant and is the sender
        allow create: if isAuthenticated() &&
          request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants &&
          request.auth.uid == request.resource.data.senderId;

        // Allow updating messages (for read receipts) if user is the receiver OR if user is an admin (for moderation)
        allow update: if isAuthenticated() &&
          (isAdmin() || (request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants &&
          request.auth.uid == request.resource.data.receiverId));

        // Allow deleting messages if user is an admin (for moderation)
        allow delete: if isAdmin();
      }
    }
    
    // Feedback collection
    match /feedback/{feedbackId} {
      // Anyone can create feedback
      // Only admins can read all feedback
      allow create: if isAuthenticated();
      allow read: if isAdmin() || isOwner(resource.data.userId);
      allow update, delete: if isAdmin();
    }
    
    // Issues collection
    match /issues/{issueId} {
      // Anyone can create an issue report
      // Only the reporter or an admin can read their own reports
      // Only admins can update or delete issues
      allow create: if isAuthenticated();
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow update, delete: if isAdmin();
    }

    // Orders collection
    match /orders/{orderId} {
      // Only the buyer, seller, or admin can read an order
      // Only the buyer can create an order
      // Buyers can update for return requests, sellers can update for delivery status, admins can update anything
      allow read: if isAuthenticated() &&
        (request.auth.uid == resource.data.buyerId ||
         request.auth.uid == resource.data.sellerId ||
         isAdmin());
      allow create: if isAuthenticated() && request.resource.data.buyerId == request.auth.uid;
      allow update: if isAuthenticated() &&
        (request.auth.uid == resource.data.buyerId ||
         request.auth.uid == resource.data.sellerId ||
         isAdmin());
      allow delete: if isAdmin();
    }

    // Secret codes collection for delivery confirmation
    match /codes/{orderId} {
      // Only the buyer of the order can read the code
      // Only the system (admin) can create/update codes
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/orders/$(orderId)) &&
        request.auth.uid == get(/databases/$(database)/documents/orders/$(orderId)).data.buyerId;
      allow write: if isAdmin();
    }

    // Shipping labels collection
    match /shippingLabels/{orderId} {
      // Only the buyer, seller, or admin can read shipping labels
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/orders/$(orderId)) &&
        (request.auth.uid == get(/databases/$(database)/documents/orders/$(orderId)).data.buyerId ||
         request.auth.uid == get(/databases/$(database)/documents/orders/$(orderId)).data.sellerId ||
         isAdmin());
      allow write: if isAdmin();
    }

    // Pending payouts collection
    match /pendingPayouts/{orderId} {
      // Only the seller or admin can read pending payouts
      allow read: if isAuthenticated() &&
        (request.auth.uid == resource.data.sellerId || isAdmin());
      allow write: if isAdmin();
    }

    // Complaints collection
    match /complaints/{complaintId} {
      // Anyone can create a complaint
      // Only the complainant or an admin can read their own complaints
      // Only admins can update or delete complaints
      allow create: if isAuthenticated();
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow update, delete: if isAdmin();
    }
    
    // ReeFlex activity collection
    match /reeflex_activity/{eventId} {
      // Anyone can create events
      // Only admins can read all events
      allow create: if true; // Allow even unauthenticated users to log events
      allow read: if isAdmin();
      allow update, delete: if isAdmin();
    }
    
    // ReeFlex feedback collection
    match /reeflex_feedback/{feedbackId} {
      // Anyone can create feedback
      // Only admins can read all feedback
      allow create: if true; // Allow even unauthenticated users to submit feedback
      allow read: if isAdmin() || (isAuthenticated() && resource.data.userId == request.auth.uid);
      allow update, delete: if isAdmin();
    }
    
    // ReeFlex reports collection
    match /reeflex_reports/{reportId} {
      // Only admins can read or write reports
      allow read, write: if isAdmin();
    }

    // Admin settings collection
    match /adminSettings/{settingId} {
      // Only admins can read or write admin settings
      allow read, write: if isAdmin();
    }

    // Reports collection (for admin dashboard)
    match /reports/{reportId} {
      // Anyone can create reports
      // Only admins can read all reports
      allow create: if isAuthenticated();
      allow read: if isAdmin() || isOwner(resource.data.userId);
      allow update, delete: if isAdmin();
    }

    // Shipping labels collection
    match /shippingLabels/{labelId} {
      // Only admins and order participants can read shipping labels
      allow read: if isAdmin() ||
        (isAuthenticated() &&
         (request.auth.uid == resource.data.buyerId ||
          request.auth.uid == resource.data.sellerId));
      // Only admins can write shipping labels
      allow write: if isAdmin();
    }

    // Wallet transactions collection
    match /walletTransactions/{transactionId} {
      // Users can read their own transactions, admins can read all
      allow read: if isAdmin() || isOwner(resource.data.userId);
      // Only system/admin can write wallet transactions
      allow write: if isAdmin();
    }

    // Wallet reports collection
    match /walletReports/{reportId} {
      // Only admins can read or write wallet reports
      allow read, write: if isAdmin();
    }

    // University analytics collection
    match /universityAnalytics/{analyticsId} {
      // Only admins can read or write university analytics
      allow read, write: if isAdmin();
    }

    // System metrics collection
    match /systemMetrics/{metricId} {
      // Only admins can read or write system metrics
      allow read, write: if isAdmin();
    }

    // Admin activity logs
    match /adminLogs/{logId} {
      // Only admins can read or write admin logs
      allow read, write: if isAdmin();
    }

    // Admin notifications collection - SECURITY FIX: Ensure only admins with custom claims
    match /admin_notifications/{notificationId} {
      // Only users with admin custom claim can read or write admin notifications
      allow read, write: if isAuthenticated() && request.auth.token.admin == true;
    }

    // Top-level notifications collection (for broadcast notifications)
    match /notifications/{notificationId} {
      // Only admins can create notifications
      allow create: if isAdmin();
      // Users can read their own notifications
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      // Users can update their own notifications (mark as read, dismissed)
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
      // Only admins can delete notifications
      allow delete: if isAdmin();
    }

    // Admin settings document
    match /admin/{document} {
      // Only admins can read or write admin settings
      allow read, write: if isAdmin();
    }

    // Daily wallet reports collection
    match /dailyWalletReports/{reportId} {
      // Only admins can read wallet reports
      allow read: if isAdmin();
      // System can write reports (for automated generation)
      allow write: if isAdmin();
    }

    // Wallet analytics collection
    match /walletAnalytics/{analyticsId} {
      // Only admins can read wallet analytics
      allow read: if isAdmin();
      // System can write analytics (for automated generation)
      allow write: if isAdmin();
    }

    // Wallet transactions collection (for admin reporting)
    match /walletTransactions/{transactionId} {
      // Users can read their own transactions
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      // Users can create their own transactions
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      // Admins can read all transactions
      allow read: if isAdmin();
      // System can write transactions
      allow write: if isAdmin();
    }

    // Reports collection (for admin moderation)
    match /reports/{reportId} {
      // Users can create reports
      allow create: if isAuthenticated();
      // Users can read their own reports
      allow read: if isAuthenticated() && resource.data.reporterId == request.auth.uid;
      // Admins can read and update all reports
      allow read, update: if isAdmin();
    }

    // Stripe Connect accounts collection
    match /connectAccounts/{userId} {
      // Users can read and write their own connect account
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
      // Admins can read all connect accounts
      allow read: if isAdmin();
    }

    // SECURITY ADDITIONS: Rate limiting and secure collections

    // Rate limiting collection - SECURITY: Only system can access
    match /rateLimits/{limitId} {
      // Only Cloud Functions (system) can read/write rate limits
      allow read, write: if false; // Deny all client access
    }

    // Secret codes collection - SECURITY: Only system can access
    match /secretCodes/{codeId} {
      // Only Cloud Functions (system) can read/write secret codes
      allow read, write: if false; // Deny all client access
    }

    // Wallet transactions collection - SECURITY: Users can only read their own
    match /walletTransactions/{transactionId} {
      // Users can only read their own wallet transactions
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      // Only Cloud Functions (system) can write transactions
      allow write: if false; // Deny all client writes
    }

    // Webhook logs collection - SECURITY: Only system can access
    match /webhookLogs/{logId} {
      // Only Cloud Functions (system) can read/write webhook logs
      allow read, write: if false; // Deny all client access
    }

    // NEW LOGIC SECURITY COLLECTIONS

    // Disputes collection - SECURITY: Buyers can create, admins can manage
    match /disputes/{disputeId} {
      // Buyers can read their own disputes, sellers can read disputes about their orders
      allow read: if isAuthenticated() && (
        request.auth.uid == resource.data.buyerId ||
        request.auth.uid == resource.data.sellerId ||
        isAdmin()
      );
      // Only buyers can create disputes for their orders
      allow create: if isAuthenticated() && request.auth.uid == request.resource.data.buyerId;
      // Only admins can update/resolve disputes
      allow update: if isAdmin();
      // Only admins can delete disputes
      allow delete: if isAdmin();
    }

    // Abuse reports collection - SECURITY: Users can report, admins can manage
    match /abuseReports/{reportId} {
      // Users can read their own reports, admins can read all
      allow read: if isAuthenticated() && (
        request.auth.uid == resource.data.reporterId ||
        isAdmin()
      );
      // Users can create abuse reports
      allow create: if isAuthenticated() && request.auth.uid == request.resource.data.reporterId;
      // Only admins can update/resolve reports
      allow update: if isAdmin();
      // Only admins can delete reports
      allow delete: if isAdmin();
    }

    // Order status logs collection - SECURITY: Read-only for participants, system writes
    match /orderStatusLogs/{logId} {
      // Order participants can read status logs for their orders
      allow read: if isAuthenticated() && (
        isAdmin() ||
        // Check if user is buyer or seller of the order
        exists(/databases/$(database)/documents/orders/$(resource.data.orderId)) &&
        (request.auth.uid == get(/databases/$(database)/documents/orders/$(resource.data.orderId)).data.buyerId ||
         request.auth.uid == get(/databases/$(database)/documents/orders/$(resource.data.orderId)).data.sellerId)
      );
      // Only system can write status logs
      allow write: if false; // System only
    }

    // Moderation queue collection - SECURITY: System only
    match /moderationQueue/{queueId} {
      // Only system can access moderation queue
      allow read, write: if false; // System only
    }

    // Moderation logs collection - SECURITY: Admins can read, system writes
    match /moderationLogs/{logId} {
      // Only admins can read moderation logs
      allow read: if isAdmin();
      // Only system can write moderation logs
      allow write: if false; // System only
    }

    // Behavior analysis collection - SECURITY: Admins only
    match /behaviorAnalysis/{analysisId} {
      // Only admins can read behavior analysis
      allow read: if isAdmin();
      // Only system can write behavior analysis
      allow write: if false; // System only
    }

    // Shipping logs collection - SECURITY: Sellers can read own, admins can read all
    match /shippingLogs/{logId} {
      // Sellers can read their own shipping logs, admins can read all
      allow read: if isAuthenticated() && (
        request.auth.uid == resource.data.sellerId ||
        isAdmin()
      );
      // Only system can write shipping logs
      allow write: if false; // System only
    }
  }

  // CUSTOM FUNCTIONS FOR VALIDATION

  // Validate order status transitions (placeholder - actual logic in Cloud Functions)
  function isValidOrderStatusTransition(currentStatus, newStatus) {
    // Basic validation - detailed logic handled in Cloud Functions
    return currentStatus != newStatus && (
      // Allow progression to common next states
      (currentStatus == 'pending' && newStatus in ['payment_processing', 'cancelled']) ||
      (currentStatus == 'payment_succeeded' && newStatus in ['confirmed', 'refunded']) ||
      (currentStatus == 'confirmed' && newStatus in ['shipped', 'cancelled']) ||
      (currentStatus == 'shipped' && newStatus in ['in_transit', 'delivered']) ||
      (currentStatus == 'delivered' && newStatus in ['completed', 'disputed']) ||
      // Admin can override most transitions
      isAdmin()
    );
  }
}