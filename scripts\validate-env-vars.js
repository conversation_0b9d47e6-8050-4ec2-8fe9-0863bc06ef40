#!/usr/bin/env node

/**
 * 🔐 ENVIRONMENT VARIABLES SECURITY VALIDATION
 * 
 * Validates that all required environment variables are properly configured
 * and no hardcoded secrets exist in the codebase
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

const REQUIRED_ENV_VARS = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN', 
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID',
  'VITE_STRIPE_PUBLISHABLE_KEY'
];

const SENSITIVE_PATTERNS = [
  { pattern: /AIzaSy[A-Za-z0-9_-]{33}/, name: 'Firebase API Key', severity: 'CRITICAL' },
  { pattern: /pk_live_[A-Za-z0-9]{99}/, name: 'Stripe Live Key', severity: 'CRITICAL' },
  { pattern: /pk_test_[A-Za-z0-9]{99}/, name: 'Stripe Test Key', severity: 'HIGH' },
  { pattern: /sk_live_[A-Za-z0-9]{99}/, name: 'Stripe Secret Key', severity: 'CRITICAL' },
  { pattern: /sk_test_[A-Za-z0-9]{99}/, name: 'Stripe Test Secret', severity: 'HIGH' },
  { pattern: /whsec_[A-Za-z0-9]{32}/, name: 'Stripe Webhook Secret', severity: 'CRITICAL' },
  { pattern: /https:\/\/[a-z0-9]+@sentry\.io\/[0-9]+/, name: 'Sentry DSN', severity: 'MEDIUM' }
];

class EnvironmentValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
  }

  async validateEnvironmentVariables() {
    console.log('🔍 Validating environment variables...');

    // Check .env.production file
    const envPath = path.join(process.cwd(), '.env.production');
    if (!fs.existsSync(envPath)) {
      this.errors.push('❌ .env.production file not found');
      return;
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = this.parseEnvFile(envContent);

    // Check required variables
    REQUIRED_ENV_VARS.forEach(varName => {
      if (!envVars[varName]) {
        this.errors.push(`❌ Missing required environment variable: ${varName}`);
      } else if (this.isPlaceholderValue(envVars[varName])) {
        this.warnings.push(`⚠️ Environment variable ${varName} contains placeholder value`);
      } else {
        this.passed.push(`✅ ${varName} is configured`);
      }
    });

    // Validate Stripe key format
    if (envVars.VITE_STRIPE_PUBLISHABLE_KEY) {
      if (!envVars.VITE_STRIPE_PUBLISHABLE_KEY.startsWith('pk_live_') && 
          !envVars.VITE_STRIPE_PUBLISHABLE_KEY.startsWith('pk_test_')) {
        this.errors.push('❌ VITE_STRIPE_PUBLISHABLE_KEY must start with pk_live_ or pk_test_');
      } else if (envVars.VITE_STRIPE_PUBLISHABLE_KEY.startsWith('pk_live_')) {
        this.passed.push('✅ Stripe live key format is correct');
      } else {
        this.warnings.push('⚠️ Using Stripe test key (should be pk_live_ for production)');
      }
    }
  }

  async scanForHardcodedSecrets() {
    console.log('🔍 Scanning for hardcoded secrets...');

    const filesToScan = await glob([
      'src/**/*.{ts,tsx,js,jsx}',
      'public/**/*.js',
      'scripts/**/*.{js,html}',
      '*.{html,js,ts}'
    ], {
      ignore: [
        'node_modules/**',
        'dist/**',
        'build/**',
        '.git/**',
        '**/*.test.{js,ts}',
        '**/*.spec.{js,ts}'
      ]
    });

    let secretsFound = 0;

    for (const filePath of filesToScan) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        for (const { pattern, name, severity } of SENSITIVE_PATTERNS) {
          const matches = content.match(pattern);
          if (matches) {
            secretsFound++;
            const message = `${severity === 'CRITICAL' ? '🚨' : severity === 'HIGH' ? '❌' : '⚠️'} ${name} found in ${filePath}`;
            
            if (severity === 'CRITICAL') {
              this.errors.push(message);
            } else if (severity === 'HIGH') {
              this.errors.push(message);
            } else {
              this.warnings.push(message);
            }
          }
        }
      } catch (error) {
        this.warnings.push(`⚠️ Could not scan file ${filePath}: ${error.message}`);
      }
    }

    if (secretsFound === 0) {
      this.passed.push('✅ No hardcoded secrets found in codebase');
    }
  }

  async validateFirebaseConfig() {
    console.log('🔥 Validating Firebase configuration...');

    const configPath = path.join(process.cwd(), 'src', 'firebase', 'config.ts');
    if (!fs.existsSync(configPath)) {
      this.errors.push('❌ Firebase config file not found');
      return;
    }

    const configContent = fs.readFileSync(configPath, 'utf8');

    // Check that config uses environment variables
    const envVarPattern = /import\.meta\.env\.VITE_FIREBASE_/g;
    const envVarMatches = configContent.match(envVarPattern);

    if (!envVarMatches || envVarMatches.length < 6) {
      this.errors.push('❌ Firebase config does not properly use environment variables');
    } else {
      this.passed.push('✅ Firebase config properly uses environment variables');
    }

    // Check for hardcoded values
    const hardcodedPatterns = [
      /apiKey:\s*["'][^"']*AIzaSy[^"']*["']/,
      /authDomain:\s*["'][^"']*\.firebaseapp\.com["']/,
      /projectId:\s*["'][^"']*["']/
    ];

    let hasHardcodedValues = false;
    hardcodedPatterns.forEach(pattern => {
      if (pattern.test(configContent) && !configContent.includes('import.meta.env')) {
        hasHardcodedValues = true;
      }
    });

    if (hasHardcodedValues) {
      this.errors.push('❌ Firebase config contains hardcoded values');
    }
  }

  async validateServiceWorker() {
    console.log('🔧 Validating service worker configuration...');

    const swPath = path.join(process.cwd(), 'public', 'firebase-messaging-sw.js');
    if (!fs.existsSync(swPath)) {
      this.warnings.push('⚠️ Firebase messaging service worker not found');
      return;
    }

    const swContent = fs.readFileSync(swPath, 'utf8');

    // Check that service worker uses placeholder system
    if (swContent.includes('self.FIREBASE_API_KEY') || swContent.includes('PLACEHOLDER')) {
      this.passed.push('✅ Service worker uses secure configuration injection');
    } else if (swContent.includes('AIzaSy')) {
      this.errors.push('❌ Service worker contains hardcoded Firebase API key');
    }
  }

  parseEnvFile(content) {
    const envVars = {};
    content.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    return envVars;
  }

  isPlaceholderValue(value) {
    const placeholderPatterns = [
      /your_.*_here/i,
      /placeholder/i,
      /replace.*with/i,
      /^your[_-]/i,
      /example/i
    ];
    
    return placeholderPatterns.some(pattern => pattern.test(value));
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('🔐 ENVIRONMENT VARIABLES SECURITY REPORT');
    console.log('='.repeat(60));

    console.log(`\n✅ PASSED CHECKS (${this.passed.length}):`);
    this.passed.forEach(check => console.log(`   ${check}`));

    if (this.warnings.length > 0) {
      console.log(`\n⚠️ WARNINGS (${this.warnings.length}):`);
      this.warnings.forEach(warning => console.log(`   ${warning}`));
    }

    if (this.errors.length > 0) {
      console.log(`\n❌ ERRORS (${this.errors.length}):`);
      this.errors.forEach(error => console.log(`   ${error}`));
    }

    console.log('\n' + '='.repeat(60));

    if (this.errors.length === 0) {
      console.log('🎉 ENVIRONMENT VALIDATION PASSED!');
      console.log('✅ All environment variables are properly configured');
      console.log('✅ No hardcoded secrets found');
      return true;
    } else {
      console.log('🚨 ENVIRONMENT VALIDATION FAILED!');
      console.log('❌ Fix all errors before deployment');
      return false;
    }
  }

  async run() {
    console.log('🔐 Starting Environment Variables Security Validation...\n');

    await this.validateEnvironmentVariables();
    await this.scanForHardcodedSecrets();
    await this.validateFirebaseConfig();
    await this.validateServiceWorker();

    return this.generateReport();
  }
}

// Run validation
const validator = new EnvironmentValidator();
validator.run().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Validation failed:', error);
  process.exit(1);
});
