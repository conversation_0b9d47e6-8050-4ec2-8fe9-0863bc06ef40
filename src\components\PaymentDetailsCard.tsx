import React, { useEffect } from 'react';
import { 
  CreditCard, 
  DollarSign, 
  Calendar, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Receipt, 
  Shield,
  Copy,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { usePaymentDetails, PaymentDetails } from '../hooks/usePaymentDetails';
import { formatPrice } from '../utils/priceUtils';
import toast from 'react-hot-toast';

interface PaymentDetailsCardProps {
  orderId: string;
  className?: string;
}

const PaymentDetailsCard: React.FC<PaymentDetailsCardProps> = ({ orderId, className = '' }) => {
  const { paymentDetails, isLoading, error, fetchPaymentDetails } = usePaymentDetails();

  useEffect(() => {
    if (orderId) {
      fetchPaymentDetails(orderId);
    }
  }, [orderId, fetchPaymentDetails]);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'paid':
      case 'complete':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'failed':
      case 'canceled':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'paid':
      case 'complete':
        return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20';
      case 'pending':
      case 'processing':
        return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'failed':
      case 'canceled':
        return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20';
      default:
        return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading payment details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="flex items-center text-red-600 dark:text-red-400">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>Error loading payment details: {error}</span>
        </div>
      </div>
    );
  }

  if (!paymentDetails) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          No payment details available
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Receipt className="w-6 h-6 text-primary-600 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Payment Details
            </h3>
          </div>
          {paymentDetails.stripePaymentIntent && (
            <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(paymentDetails.stripePaymentIntent.status)}`}>
              {getStatusIcon(paymentDetails.stripePaymentIntent.status)}
              <span className="ml-1 capitalize">{paymentDetails.stripePaymentIntent.status}</span>
            </div>
          )}
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Order Amount Breakdown */}
        <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
            <DollarSign className="w-4 h-4 mr-2" />
            Amount Breakdown
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Order Total:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                ${formatPrice(paymentDetails.orderAmount)}
              </span>
            </div>
            {paymentDetails.walletAmountUsed > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Wallet Credit Used:</span>
                <span className="font-medium text-green-600 dark:text-green-400">
                  -${formatPrice(paymentDetails.walletAmountUsed)}
                </span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Platform Fee:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                ${formatPrice(paymentDetails.commissionAmount)}
              </span>
            </div>
            <div className="flex justify-between border-t border-gray-200 dark:border-gray-600 pt-2">
              <span className="text-gray-600 dark:text-gray-400">Seller Receives:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                ${formatPrice(paymentDetails.sellerAmount)}
              </span>
            </div>
          </div>
        </div>

        {/* Payment Method */}
        {paymentDetails.paymentMethod && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
              <CreditCard className="w-4 h-4 mr-2" />
              Payment Method
            </h4>
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
              {paymentDetails.paymentMethod.card && (
                <div className="flex items-center">
                  <div className="w-8 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded flex items-center justify-center text-white text-xs font-bold mr-3">
                    {paymentDetails.paymentMethod.card.brand.toUpperCase()}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      •••• •••• •••• {paymentDetails.paymentMethod.card.last4}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Expires {paymentDetails.paymentMethod.card.exp_month}/{paymentDetails.paymentMethod.card.exp_year} • {paymentDetails.paymentMethod.card.funding}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Transaction Details */}
        {paymentDetails.stripePaymentIntent && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
              <Shield className="w-4 h-4 mr-2" />
              Transaction Details
            </h4>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Transaction ID:</span>
                <div className="flex items-center">
                  <span className="font-mono text-xs text-gray-900 dark:text-white mr-2">
                    {paymentDetails.stripePaymentIntent.id}
                  </span>
                  <button
                    onClick={() => copyToClipboard(paymentDetails.stripePaymentIntent!.id, 'Transaction ID')}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <Copy className="w-3 h-3" />
                  </button>
                </div>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Payment Date:</span>
                <span className="text-gray-900 dark:text-white">
                  {formatDate(paymentDetails.stripePaymentIntent.created)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Currency:</span>
                <span className="text-gray-900 dark:text-white uppercase">
                  {paymentDetails.stripePaymentIntent.currency}
                </span>
              </div>

              {paymentDetails.stripePaymentIntent.receipt_email && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Receipt Email:</span>
                  <span className="text-gray-900 dark:text-white">
                    {paymentDetails.stripePaymentIntent.receipt_email}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Receipt Link */}
        {paymentDetails.charge?.receipt_url && (
          <div>
            <a
              href={paymentDetails.charge.receipt_url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium"
            >
              <Receipt className="w-4 h-4 mr-2" />
              View Receipt
              <ExternalLink className="w-3 h-3 ml-2" />
            </a>
          </div>
        )}

        {/* Error Messages */}
        {(paymentDetails.stripeError || paymentDetails.sessionError) && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5" />
              <div className="text-sm">
                <p className="text-yellow-800 dark:text-yellow-200 font-medium mb-1">
                  Some payment details unavailable
                </p>
                {paymentDetails.stripeError && (
                  <p className="text-yellow-700 dark:text-yellow-300">{paymentDetails.stripeError}</p>
                )}
                {paymentDetails.sessionError && (
                  <p className="text-yellow-700 dark:text-yellow-300">{paymentDetails.sessionError}</p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentDetailsCard;
