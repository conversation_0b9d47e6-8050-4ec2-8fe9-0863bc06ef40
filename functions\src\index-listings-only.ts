// Minimal functions index - only createListing function
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

// Types for listings
type ListingCondition = 'new' | 'like_new' | 'very_good' | 'good' | 'fair' | 'poor';
type ListingType = 'sell' | 'rent' | 'auction';
type ListingStatus = 'active' | 'sold' | 'inactive' | 'pending';

// Helper functions
const verifyAuth = async (context: functions.https.CallableContext): Promise<admin.auth.DecodedIdToken> => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }
  return context.auth as unknown as admin.auth.DecodedIdToken;
};

const handleError = (error: unknown): never => {
  console.error('Function error:', error);

  if (error instanceof functions.https.HttpsError) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
  throw new functions.https.HttpsError(
    'internal',
    errorMessage,
    error
  );
};

console.log('🚀 Minimal Firebase Functions with Listings loading...');

// Create a new listing
export const createListing = functions.https.onCall(async (data, context) => {
  try {
    console.log('Creating listing with data:', JSON.stringify(data, null, 2));
    const auth = await verifyAuth(context);

    const {
      title,
      description,
      price,
      category,
      condition,
      type,
      imageURLs
    } = data;
    
    // Validate required fields
    if (!title || !description || price === undefined || !category || !condition || !type) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields'
      );
    }
    
    // Validate listing type
    if (!['sell', 'rent', 'auction'].includes(type)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid listing type. Must be one of: sell, rent, auction'
      );
    }
    
    // Validate condition
    if (!['new', 'like_new', 'very_good', 'good', 'fair', 'poor'].includes(condition)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid condition. Must be one of: new, like_new, very_good, good, fair, poor'
      );
    }
    
    // Get user data to include university
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'User not found'
      );
    }

    const userData = userDoc.data();
    console.log('User data:', JSON.stringify(userData, null, 2));

    // Get university from user data or extract from email
    let university = userData?.university;
    console.log('Initial university:', university);

    if (!university && userData?.email) {
      console.log('Extracting university from email:', userData.email);
      // Extract university from email domain as fallback
      const emailParts = userData.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university:', university);

      // Update user profile with university
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        updatedAt: admin.firestore.Timestamp.now()
      });
    } else if (!university && auth.token?.email) {
      console.log('Extracting university from auth token email:', auth.token.email);
      // Try to get email from auth token as fallback
      const emailParts = auth.token.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university from token:', university);

      // Update user profile with university and email
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        email: auth.token.email,
        updatedAt: admin.firestore.Timestamp.now()
      });
    }

    if (!university) {
      console.error('Unable to determine university. User data:', userData, 'Auth token:', auth.token);
      throw new functions.https.HttpsError(
        'failed-precondition',
        'Unable to determine university from user profile or email. Please update your profile.'
      );
    }

    console.log('Final university:', university);

    // Create the listing object
    const listing: any = {
      title,
      description,
      price: Number(price),
      category,
      condition: condition as ListingCondition,
      type: type as ListingType,
      ownerId: auth.uid,
      ownerName: userData?.name || 'Anonymous',
      university: university,
      imageURLs: imageURLs || [],
      status: 'active' as ListingStatus,
      visibility: data.visibility || 'university',
      createdAt: admin.firestore.Timestamp.now(),

      // Add delivery method fields
      deliveryMethod: data.deliveryMethod || 'in_person'
    };

    // Only add shippingOptions if it exists and has valid data
    if (data.shippingOptions && Object.keys(data.shippingOptions).length > 0) {
      listing.shippingOptions = data.shippingOptions;
    }

    // Add type-specific fields
    if (type === 'rent') {
      if (data.rentalPeriod) listing.rentalPeriod = data.rentalPeriod;
      if (data.weeklyPrice) listing.weeklyPrice = Number(data.weeklyPrice);
      if (data.monthlyPrice) listing.monthlyPrice = Number(data.monthlyPrice);
      if (data.startDate) listing.startDate = data.startDate;
      if (data.endDate) listing.endDate = data.endDate;
    }

    if (type === 'auction') {
      if (data.startingBid) listing.startingBid = Number(data.startingBid);
      if (data.auctionStartDate) listing.auctionStartDate = data.auctionStartDate;
      if (data.auctionStartTime) listing.auctionStartTime = data.auctionStartTime;
      if (data.auctionEndDate) listing.auctionEndDate = data.auctionEndDate;
      if (data.auctionEndTime) listing.auctionEndTime = data.auctionEndTime;
      if (data.auctionDuration) listing.auctionDuration = data.auctionDuration;
    }
    
    // Add to Firestore
    console.log('Adding listing to Firestore:', JSON.stringify(listing, null, 2));
    const docRef = await admin.firestore().collection('listings').add(listing);
    console.log('Listing created successfully with ID:', docRef.id);

    return {
      success: true,
      data: {
        id: docRef.id,
        ...listing
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Test function to verify deployment
export const testListings = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Listings functions working',
      timestamp: new Date().toISOString()
    });
  });
