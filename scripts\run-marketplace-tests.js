#!/usr/bin/env node

/**
 * 🚀 MARKETPLACE E2E TEST RUNNER
 * Comprehensive test runner for marketplace transaction flows
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class MarketplaceTestRunner {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    };
    this.startTime = Date.now();
  }

  /**
   * Print colored console output
   */
  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  /**
   * Print test header
   */
  printHeader() {
    this.log('\n' + '='.repeat(80), 'cyan');
    this.log('🏪 HIVE CAMPUS MARKETPLACE E2E TEST SUITE', 'bright');
    this.log('Complete Transaction Flow Testing', 'cyan');
    this.log('='.repeat(80), 'cyan');
    this.log(`📅 Started: ${new Date().toLocaleString()}`, 'blue');
    this.log('');
  }

  /**
   * Check prerequisites
   */
  async checkPrerequisites() {
    this.log('🔍 Checking prerequisites...', 'yellow');
    
    const checks = [
      { name: 'Node.js version', check: () => process.version },
      { name: 'Test files exist', check: () => this.checkTestFiles() },
      { name: 'Environment variables', check: () => this.checkEnvVars() },
      { name: 'Firebase config', check: () => this.checkFirebaseConfig() }
    ];

    for (const check of checks) {
      try {
        const result = await check.check();
        this.log(`  ✅ ${check.name}: ${result}`, 'green');
      } catch (error) {
        this.log(`  ❌ ${check.name}: ${error.message}`, 'red');
        throw new Error(`Prerequisite check failed: ${check.name}`);
      }
    }

    this.log('');
  }

  /**
   * Check if test files exist
   */
  checkTestFiles() {
    const requiredFiles = [
      'tests/e2e-marketplace-flow.test.js',
      'tests/helpers/test-data-factory.js',
      'tests/helpers/marketplace-test-helpers.js',
      'tests/mocks/shippo-mock.js',
      'tests/fixtures/test-users.json'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(path.join(process.cwd(), file))) {
        throw new Error(`Missing test file: ${file}`);
      }
    }

    return `${requiredFiles.length} files found`;
  }

  /**
   * Check environment variables
   */
  checkEnvVars() {
    const requiredEnvVars = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_FIREBASE_AUTH_DOMAIN'
    ];

    const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missing.length > 0) {
      throw new Error(`Missing environment variables: ${missing.join(', ')}`);
    }

    return `${requiredEnvVars.length} variables configured`;
  }

  /**
   * Check Firebase configuration
   */
  checkFirebaseConfig() {
    const configFiles = [
      '.firebaserc',
      'firebase.json'
    ];

    for (const file of configFiles) {
      if (!fs.existsSync(path.join(process.cwd(), file))) {
        throw new Error(`Missing Firebase config: ${file}`);
      }
    }

    return 'Firebase configured';
  }

  /**
   * Run the test suite
   */
  async runTests() {
    this.log('🧪 Running marketplace transaction flow tests...', 'yellow');
    this.log('');

    return new Promise((resolve, reject) => {
      const testProcess = spawn('npx', ['vitest', 'tests/e2e-marketplace-flow.test.js', '--run'], {
        stdio: 'pipe',
        env: { ...process.env, NODE_ENV: 'test' }
      });

      let output = '';
      let errorOutput = '';

      testProcess.stdout.on('data', (data) => {
        const chunk = data.toString();
        output += chunk;
        process.stdout.write(chunk);
      });

      testProcess.stderr.on('data', (data) => {
        const chunk = data.toString();
        errorOutput += chunk;
        process.stderr.write(chunk);
      });

      testProcess.on('close', (code) => {
        this.parseTestResults(output);
        
        if (code === 0) {
          resolve({ success: true, output, errorOutput });
        } else {
          reject({ success: false, output, errorOutput, code });
        }
      });

      testProcess.on('error', (error) => {
        reject({ success: false, error: error.message });
      });
    });
  }

  /**
   * Parse test results from output
   */
  parseTestResults(output) {
    // Parse Vitest output to extract test statistics
    const lines = output.split('\n');
    
    for (const line of lines) {
      if (line.includes('Test Files')) {
        const match = line.match(/(\d+) passed/);
        if (match) {
          this.testResults.passed = parseInt(match[1]);
        }
      }
      
      if (line.includes('Tests')) {
        const passedMatch = line.match(/(\d+) passed/);
        const failedMatch = line.match(/(\d+) failed/);
        const totalMatch = line.match(/(\d+) total/);
        
        if (passedMatch) this.testResults.passed = parseInt(passedMatch[1]);
        if (failedMatch) this.testResults.failed = parseInt(failedMatch[1]);
        if (totalMatch) this.testResults.total = parseInt(totalMatch[1]);
      }
    }

    this.testResults.duration = Date.now() - this.startTime;
  }

  /**
   * Print test summary
   */
  printSummary(success) {
    this.log('\n' + '='.repeat(80), 'cyan');
    this.log('📊 TEST SUMMARY', 'bright');
    this.log('='.repeat(80), 'cyan');
    
    const duration = Math.round(this.testResults.duration / 1000);
    
    this.log(`⏱️  Duration: ${duration}s`, 'blue');
    this.log(`📝 Total Tests: ${this.testResults.total}`, 'blue');
    this.log(`✅ Passed: ${this.testResults.passed}`, 'green');
    
    if (this.testResults.failed > 0) {
      this.log(`❌ Failed: ${this.testResults.failed}`, 'red');
    }
    
    if (this.testResults.skipped > 0) {
      this.log(`⏭️  Skipped: ${this.testResults.skipped}`, 'yellow');
    }

    this.log('');
    
    if (success) {
      this.log('🎉 ALL MARKETPLACE TESTS PASSED!', 'green');
      this.log('✅ Transaction flow validation complete', 'green');
    } else {
      this.log('❌ SOME TESTS FAILED', 'red');
      this.log('🔍 Check the output above for details', 'yellow');
    }
    
    this.log('='.repeat(80), 'cyan');
  }

  /**
   * Generate test report
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      duration: this.testResults.duration,
      results: this.testResults,
      scenarios: [
        'Create listing with shipping enabled',
        'Hybrid wallet + Stripe checkout',
        'Generate shipping label via Shippo',
        'Mark item as shipped',
        'Buyer confirms delivery with PIN',
        'In-person delivery flow',
        'In-person meeting and PIN entry',
        'Mail-in return flow',
        'Generate return label',
        'Refund after return received',
        'Final validation and audit',
        'Auto-release system test',
        'Error handling tests'
      ],
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        testMode: 'e2e-marketplace'
      }
    };

    const reportPath = path.join(process.cwd(), 'test-reports', 'marketplace-e2e-report.json');
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log(`📄 Test report saved: ${reportPath}`, 'blue');
  }

  /**
   * Main execution function
   */
  async run() {
    try {
      this.printHeader();
      await this.checkPrerequisites();
      
      const result = await this.runTests();
      
      this.printSummary(result.success);
      this.generateReport();
      
      process.exit(0);
    } catch (error) {
      this.log(`\n❌ Test execution failed: ${error.message || error}`, 'red');
      
      if (error.output) {
        this.log('\n📋 Error Output:', 'yellow');
        console.log(error.output);
      }
      
      this.printSummary(false);
      this.generateReport();
      
      process.exit(1);
    }
  }
}

// Run the test suite if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new MarketplaceTestRunner();
  runner.run();
}

export default MarketplaceTestRunner;
