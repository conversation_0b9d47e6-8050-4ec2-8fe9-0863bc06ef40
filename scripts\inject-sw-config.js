#!/usr/bin/env node

/**
 * 🔧 SERVICE WORKER CONFIG INJECTION SCRIPT
 * 
 * Injects Firebase environment variables into the service worker
 * during build time since service workers cannot access import.meta.env
 */

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SW_PATH = path.join(process.cwd(), 'dist', 'firebase-messaging-sw.js');
const SW_SOURCE_PATH = path.join(process.cwd(), 'public', 'firebase-messaging-sw.js');

function injectFirebaseConfig() {
  console.log('🔧 Injecting Firebase config into service worker...');

  // Check if service worker exists in dist
  if (!fs.existsSync(SW_PATH)) {
    console.log('⚠️ Service worker not found in dist, copying from public...');
    
    // Ensure dist directory exists
    const distDir = path.dirname(SW_PATH);
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true });
    }
    
    // Copy service worker to dist
    fs.copyFileSync(SW_SOURCE_PATH, SW_PATH);
  }

  // Read the service worker file
  let swContent = fs.readFileSync(SW_PATH, 'utf8');

  // Get environment variables
  const firebaseConfig = {
    FIREBASE_API_KEY: process.env.VITE_FIREBASE_API_KEY || '',
    FIREBASE_AUTH_DOMAIN: process.env.VITE_FIREBASE_AUTH_DOMAIN || '',
    FIREBASE_PROJECT_ID: process.env.VITE_FIREBASE_PROJECT_ID || '',
    FIREBASE_STORAGE_BUCKET: process.env.VITE_FIREBASE_STORAGE_BUCKET || '',
    FIREBASE_MESSAGING_SENDER_ID: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '',
    FIREBASE_APP_ID: process.env.VITE_FIREBASE_APP_ID || '',
    FIREBASE_MEASUREMENT_ID: process.env.VITE_FIREBASE_MEASUREMENT_ID || ''
  };

  // Validate that we have the required config
  const missingVars = Object.entries(firebaseConfig)
    .filter(([key, value]) => !value || value.includes('PLACEHOLDER'))
    .map(([key]) => key);

  if (missingVars.length > 0) {
    console.warn(`⚠️ Missing Firebase environment variables: ${missingVars.join(', ')}`);
    console.warn('Service worker will use placeholder values');
  }

  // Inject config at the top of the service worker
  const configInjection = `
// Firebase configuration injected at build time
self.FIREBASE_API_KEY = "${firebaseConfig.FIREBASE_API_KEY}";
self.FIREBASE_AUTH_DOMAIN = "${firebaseConfig.FIREBASE_AUTH_DOMAIN}";
self.FIREBASE_PROJECT_ID = "${firebaseConfig.FIREBASE_PROJECT_ID}";
self.FIREBASE_STORAGE_BUCKET = "${firebaseConfig.FIREBASE_STORAGE_BUCKET}";
self.FIREBASE_MESSAGING_SENDER_ID = "${firebaseConfig.FIREBASE_MESSAGING_SENDER_ID}";
self.FIREBASE_APP_ID = "${firebaseConfig.FIREBASE_APP_ID}";
self.FIREBASE_MEASUREMENT_ID = "${firebaseConfig.FIREBASE_MEASUREMENT_ID}";

`;

  // Insert config injection after the imports
  const importScriptsRegex = /(importScripts\([^)]+\);\s*\n)/g;
  const matches = [...swContent.matchAll(importScriptsRegex)];
  
  if (matches.length > 0) {
    const lastImportIndex = matches[matches.length - 1].index + matches[matches.length - 1][0].length;
    swContent = swContent.slice(0, lastImportIndex) + configInjection + swContent.slice(lastImportIndex);
  } else {
    // If no imports found, add at the beginning
    swContent = configInjection + swContent;
  }

  // Write the updated service worker
  fs.writeFileSync(SW_PATH, swContent);

  console.log('✅ Firebase config injected into service worker successfully');
  
  // Log what was injected (without sensitive values)
  console.log('📋 Injected configuration:');
  Object.entries(firebaseConfig).forEach(([key, value]) => {
    const displayValue = value ? (value.length > 10 ? `${value.substring(0, 10)}...` : value) : 'NOT_SET';
    console.log(`   ${key}: ${displayValue}`);
  });
}

// Run the injection
try {
  injectFirebaseConfig();
} catch (error) {
  console.error('❌ Failed to inject Firebase config:', error);
  process.exit(1);
}
