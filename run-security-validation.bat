@echo off
echo.
echo ================================================================
echo 🔐 HIVE CAMPUS COMPREHENSIVE SECURITY VALIDATION
echo ================================================================
echo.
echo 🎯 Objective: Complete security, logic, and production validation
echo 📅 Started: %date% %time%
echo.
echo ================================================================
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm not found. Please install npm first.
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed
    echo.
)

REM Build the application
echo 🏗️ Building application...
npm run build
if errorlevel 1 (
    echo ❌ Build failed
    pause
    exit /b 1
)
echo ✅ Build completed
echo.

REM Run comprehensive validation
echo 🔐 Starting comprehensive security validation...
echo.

REM Phase 1: Security Tests
echo 🛡️ PHASE 1: SECURITY VALIDATION
echo ----------------------------------------
node scripts/comprehensive-security-audit.js
if errorlevel 1 (
    echo ❌ Security audit failed
) else (
    echo ✅ Security audit completed
)
echo.

REM Phase 2: Load Testing
echo 📈 PHASE 2: LOAD TESTING & ABUSE SIMULATION
echo ----------------------------------------
node scripts/load-testing.js
if errorlevel 1 (
    echo ❌ Load testing failed
) else (
    echo ✅ Load testing completed
)
echo.

REM Phase 3: Production Validation
echo 🚀 PHASE 3: PRODUCTION DEPLOYMENT VALIDATION
echo ----------------------------------------
node scripts/production-deployment-checklist.js
if errorlevel 1 (
    echo ❌ Production validation failed
) else (
    echo ✅ Production validation completed
)
echo.

REM Phase 4: Unit Tests
echo 🧪 PHASE 4: UNIT & INTEGRATION TESTS
echo ----------------------------------------
npm run test:coverage
if errorlevel 1 (
    echo ❌ Unit tests failed
) else (
    echo ✅ Unit tests completed
)
echo.

REM Phase 5: E2E Tests (optional, may not be set up)
echo 🎭 PHASE 5: END-TO-END TESTS
echo ----------------------------------------
npm run test:e2e
if errorlevel 1 (
    echo ⚠️ E2E tests failed or not configured
) else (
    echo ✅ E2E tests completed
)
echo.

REM Phase 6: Performance Audit
echo ⚡ PHASE 6: PERFORMANCE AUDIT
echo ----------------------------------------
npm run lighthouse
if errorlevel 1 (
    echo ⚠️ Performance audit failed or not configured
) else (
    echo ✅ Performance audit completed
)
echo.

REM Final Comprehensive Report
echo 🎯 GENERATING FINAL COMPREHENSIVE REPORT
echo ----------------------------------------
node scripts/run-comprehensive-validation.js
if errorlevel 1 (
    echo ❌ Final validation failed
) else (
    echo ✅ Final validation completed
)
echo.

echo ================================================================
echo 🏆 HIVE CAMPUS SECURITY VALIDATION COMPLETE
echo ================================================================
echo.
echo 📄 Check the generated reports for detailed results:
echo    - security-audit-*.json
echo    - load-test-report-*.json  
echo    - production-deployment-report-*.json
echo    - hive-campus-validation-report-*.json
echo.
echo 📋 Review the COMPREHENSIVE_SECURITY_VALIDATION_SUMMARY.md
echo    for complete validation details and recommendations.
echo.

REM Check for critical issues in the latest reports
echo 🔍 QUICK RESULTS SUMMARY:
echo ----------------------------------------

REM Find the latest security audit report
for /f "delims=" %%i in ('dir /b /o-d security-audit-*.json 2^>nul') do (
    set "latest_security=%%i"
    goto :found_security
)
:found_security

if defined latest_security (
    echo 🔐 Security Audit: Report generated - %latest_security%
) else (
    echo 🔐 Security Audit: No report found
)

REM Find the latest load test report
for /f "delims=" %%i in ('dir /b /o-d load-test-report-*.json 2^>nul') do (
    set "latest_load=%%i"
    goto :found_load
)
:found_load

if defined latest_load (
    echo 📈 Load Testing: Report generated - %latest_load%
) else (
    echo 📈 Load Testing: No report found
)

REM Find the latest production report
for /f "delims=" %%i in ('dir /b /o-d production-deployment-report-*.json 2^>nul') do (
    set "latest_prod=%%i"
    goto :found_prod
)
:found_prod

if defined latest_prod (
    echo 🚀 Production Validation: Report generated - %latest_prod%
) else (
    echo 🚀 Production Validation: No report found
)

REM Find the latest comprehensive report
for /f "delims=" %%i in ('dir /b /o-d hive-campus-validation-report-*.json 2^>nul') do (
    set "latest_comprehensive=%%i"
    goto :found_comprehensive
)
:found_comprehensive

if defined latest_comprehensive (
    echo 🏆 Comprehensive Report: %latest_comprehensive%
    echo.
    echo 🎉 VALIDATION COMPLETE! Check the comprehensive report for final certification status.
) else (
    echo 🏆 Comprehensive Report: Not generated
    echo.
    echo ⚠️ Validation may have encountered issues. Check individual reports.
)

echo.
echo ================================================================
echo 📞 NEXT STEPS:
echo ================================================================
echo.
echo 1. Review all generated reports for detailed findings
echo 2. Address any critical issues identified
echo 3. Re-run validation if fixes were applied
echo 4. Deploy to production once certified
echo.
echo For support, refer to COMPREHENSIVE_SECURITY_VALIDATION_SUMMARY.md
echo.
pause
