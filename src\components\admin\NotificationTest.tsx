import React, { useState } from 'react';
import { Send, TestTube, User, Users, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { AdminDataService } from '../../services/AdminDataService';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../../firebase/config';

const NotificationTest: React.FC = () => {
  const { currentUser, isAdmin } = useAuth();
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const testBroadcastFunction = async () => {
    if (!currentUser || !isAdmin) {
      setError('Admin authentication required');
      return;
    }

    setTesting(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing broadcast notification function...');
      
      const sendBroadcastFunction = httpsCallable(functions, 'sendBroadcastNotification');
      
      const result = await sendBroadcastFunction({
        title: 'Test Notification',
        message: 'This is a test broadcast notification from the admin panel.',
        targetRole: 'all'
      });

      console.log('Function result:', result.data);
      setResult(`Success: ${JSON.stringify(result.data, null, 2)}`);
      
    } catch (err: any) {
      console.error('Function test error:', err);
      setError(`Error: ${err.code || 'Unknown'} - ${err.message || 'Unknown error'}`);
    } finally {
      setTesting(false);
    }
  };

  const testDirectService = async () => {
    if (!currentUser || !isAdmin) {
      setError('Admin authentication required');
      return;
    }

    setTesting(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing direct service call...');
      
      await AdminDataService.sendBroadcastNotification(
        currentUser.uid,
        'Test Direct Service',
        'This is a test notification using the direct service method.',
        'all'
      );

      setResult('Direct service call completed successfully');
      
    } catch (err: any) {
      console.error('Direct service test error:', err);
      setError(`Error: ${err.code || 'Unknown'} - ${err.message || 'Unknown error'}`);
    } finally {
      setTesting(false);
    }
  };

  const testUserNotification = async () => {
    if (!currentUser || !isAdmin) {
      setError('Admin authentication required');
      return;
    }

    setTesting(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing user notification function...');
      
      const sendUserNotificationFunction = httpsCallable(functions, 'sendUserNotification');
      
      const result = await sendUserNotificationFunction({
        userId: currentUser.uid, // Send to self for testing
        title: 'Test User Notification',
        message: 'This is a test user notification.',
        type: 'message'
      });

      console.log('User notification result:', result.data);
      setResult(`User notification success: ${JSON.stringify(result.data, null, 2)}`);
      
    } catch (err: any) {
      console.error('User notification test error:', err);
      setError(`Error: ${err.code || 'Unknown'} - ${err.message || 'Unknown error'}`);
    } finally {
      setTesting(false);
    }
  };

  if (!isAdmin) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
          <p className="text-sm text-yellow-600 dark:text-yellow-400">
            Admin privileges required to test notifications
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center mb-4">
        <TestTube className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Notification System Test
        </h3>
      </div>

      <div className="space-y-4">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
            <div className="flex items-start">
              <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mr-2 mt-0.5" />
              <pre className="text-sm text-red-600 dark:text-red-400 whitespace-pre-wrap">{error}</pre>
            </div>
          </div>
        )}

        {result && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
            <div className="flex items-start">
              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mr-2 mt-0.5" />
              <pre className="text-sm text-green-600 dark:text-green-400 whitespace-pre-wrap">{result}</pre>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={testBroadcastFunction}
            disabled={testing}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Users className="h-4 w-4 mr-2" />
            {testing ? 'Testing...' : 'Test Broadcast Function'}
          </button>

          <button
            onClick={testDirectService}
            disabled={testing}
            className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4 mr-2" />
            {testing ? 'Testing...' : 'Test Direct Service'}
          </button>

          <button
            onClick={testUserNotification}
            disabled={testing}
            className="flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <User className="h-4 w-4 mr-2" />
            {testing ? 'Testing...' : 'Test User Notification'}
          </button>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
          <p><strong>Current User:</strong> {currentUser?.email}</p>
          <p><strong>User ID:</strong> {currentUser?.uid}</p>
          <p><strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}</p>
        </div>
      </div>
    </div>
  );
};

export default NotificationTest;
