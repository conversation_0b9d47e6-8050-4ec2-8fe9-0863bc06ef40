/**
 * 🔐 COMPREHENSIVE SECURITY VALIDATION SUITE
 * Hive Campus Production Security Testing
 * 
 * Tests all security layers:
 * - Firestore Rules Exploitation
 * - Authentication Brute Force
 * - Webhook Security
 * - Race Conditions
 * - Input Validation
 * - Admin Access Control
 */

import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator, doc, getDoc, setDoc, collection, addDoc } from 'firebase/firestore';
import { getAuth, connectAuthEmulator, signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { getFunctions, connectFunctionsEmulator, httpsCallable } from 'firebase/functions';

// Test configuration
const FIREBASE_CONFIG = {
  apiKey: "test-api-key",
  authDomain: "test-project.firebaseapp.com",
  projectId: "test-project",
  storageBucket: "test-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "test-app-id"
};

let app, db, auth, functions;
let testUsers = [];

describe('🔐 SECURITY VALIDATION SUITE', () => {
  beforeAll(async () => {
    // Initialize Firebase with emulators for testing
    app = initializeApp(FIREBASE_CONFIG);
    db = getFirestore(app);
    auth = getAuth(app);
    functions = getFunctions(app);

    // Connect to emulators if running locally
    if (process.env.NODE_ENV === 'test') {
      connectFirestoreEmulator(db, 'localhost', 8080);
      connectAuthEmulator(auth, 'http://localhost:9099');
      connectFunctionsEmulator(functions, 'localhost', 5001);
    }

    // Create test users
    testUsers = await createTestUsers();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
  });

  describe('🛡️ FIRESTORE RULES EXPLOIT TESTING', () => {
    it('should prevent reading other users profile data', async () => {
      const [user1, user2] = testUsers;
      
      // Try to read another user's profile
      try {
        const userDoc = doc(db, 'users', user2.uid);
        await getDoc(userDoc);
        expect.fail('Should not be able to read other user profiles');
      } catch (error) {
        expect(error.code).toBe('permission-denied');
      }
    });

    it('should prevent unauthorized access to admin collections', async () => {
      const regularUser = testUsers[0];
      
      try {
        const adminDoc = doc(db, 'admin', 'settings');
        await getDoc(adminDoc);
        expect.fail('Regular users should not access admin collections');
      } catch (error) {
        expect(error.code).toBe('permission-denied');
      }
    });

    it('should prevent access to rate limiting collection', async () => {
      try {
        const rateLimitDoc = doc(db, 'rateLimits', 'test-limit');
        await getDoc(rateLimitDoc);
        expect.fail('Clients should not access rate limiting data');
      } catch (error) {
        expect(error.code).toBe('permission-denied');
      }
    });

    it('should prevent access to secret codes collection', async () => {
      try {
        const secretDoc = doc(db, 'secretCodes', 'test-code');
        await getDoc(secretDoc);
        expect.fail('Clients should not access secret codes');
      } catch (error) {
        expect(error.code).toBe('permission-denied');
      }
    });

    it('should prevent unauthorized wallet transaction writes', async () => {
      const user = testUsers[0];
      
      try {
        const transactionRef = collection(db, 'walletTransactions');
        await addDoc(transactionRef, {
          userId: user.uid,
          amount: 1000,
          type: 'credit',
          description: 'Fake transaction'
        });
        expect.fail('Clients should not write wallet transactions');
      } catch (error) {
        expect(error.code).toBe('permission-denied');
      }
    });
  });

  describe('🔒 AUTHENTICATION BRUTE FORCE TESTING', () => {
    it('should rate limit admin PIN attempts', async () => {
      const verifyAdminPin = httpsCallable(functions, 'verifyAdminPin');
      
      // Attempt multiple failed PIN verifications
      const attempts = [];
      for (let i = 0; i < 6; i++) {
        attempts.push(
          verifyAdminPin({ pin: '000000000000' }).catch(err => err)
        );
      }
      
      const results = await Promise.all(attempts);
      
      // Should be rate limited after 5 attempts
      const rateLimitedAttempts = results.filter(
        result => result.code === 'resource-exhausted'
      );
      
      expect(rateLimitedAttempts.length).toBeGreaterThan(0);
    });

    it('should prevent Microsoft SSO bypass attempts', async () => {
      // Test invalid .edu email rejection
      try {
        await createUserWithEmailAndPassword(auth, '<EMAIL>', 'password123');
        expect.fail('Should reject non-.edu emails');
      } catch (error) {
        expect(error.code).toBe('auth/invalid-email');
      }
    });
  });

  describe('🔐 WEBHOOK EXPLOIT SIMULATION', () => {
    it('should reject webhooks without valid signature', async () => {
      const mockWebhookPayload = {
        id: 'evt_test',
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test',
            amount: 1000,
            metadata: {
              orderId: 'test-order',
              buyerId: 'test-buyer',
              sellerId: 'test-seller'
            }
          }
        }
      };

      // Simulate webhook call without signature
      const response = await fetch('/api/stripe-webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(mockWebhookPayload)
      });

      expect(response.status).toBe(400);
    });

    it('should reject replay attacks with old timestamps', async () => {
      // Test with timestamp older than 5 minutes
      const oldTimestamp = Math.floor(Date.now() / 1000) - 400; // 6+ minutes ago
      
      const mockEvent = {
        id: 'evt_test',
        created: oldTimestamp,
        type: 'payment_intent.succeeded'
      };

      // This should be rejected by timestamp validation
      expect(() => {
        // Simulate SecureStripeWebhook.validateEventStructure
        const eventAge = Date.now() / 1000 - mockEvent.created;
        if (eventAge > 300) {
          throw new Error('Event too old');
        }
      }).toThrow('Event too old');
    });
  });

  describe('⚡ RACE CONDITION SIMULATION', () => {
    it('should prevent double-spending in wallet transactions', async () => {
      const user = testUsers[0];
      const deductWalletBalance = httpsCallable(functions, 'deductWalletBalance');
      
      // Set initial wallet balance
      await setDoc(doc(db, 'wallets', user.uid), {
        balance: 100,
        userId: user.uid
      });

      // Simulate concurrent deduction attempts
      const concurrentDeductions = [
        deductWalletBalance({ 
          amount: 60, 
          orderId: 'order1',
          description: 'Test purchase 1'
        }),
        deductWalletBalance({ 
          amount: 60, 
          orderId: 'order2',
          description: 'Test purchase 2'
        })
      ];

      const results = await Promise.allSettled(concurrentDeductions);
      
      // Only one should succeed due to atomic transactions
      const successful = results.filter(r => r.status === 'fulfilled');
      const failed = results.filter(r => r.status === 'rejected');
      
      expect(successful.length).toBe(1);
      expect(failed.length).toBe(1);
    });
  });

  describe('🔑 SECRET CODE ATTACK SIMULATION', () => {
    it('should prevent secret code reuse', async () => {
      const releaseFundsWithCode = httpsCallable(functions, 'releaseFundsWithCode');
      
      // Create test order with secret code
      const testOrder = {
        id: 'test-order-123',
        buyerId: testUsers[0].uid,
        sellerId: testUsers[1].uid,
        status: 'delivered',
        secretCode: 'ABC123',
        fundsReleased: false
      };

      await setDoc(doc(db, 'orders', testOrder.id), testOrder);

      // First attempt should succeed
      const firstAttempt = await releaseFundsWithCode({
        orderId: testOrder.id,
        secretCode: 'ABC123'
      });
      
      expect(firstAttempt.data.success).toBe(true);

      // Second attempt with same code should fail
      try {
        await releaseFundsWithCode({
          orderId: testOrder.id,
          secretCode: 'ABC123'
        });
        expect.fail('Should not allow code reuse');
      } catch (error) {
        expect(error.code).toBe('failed-precondition');
      }
    });

    it('should reject expired secret codes', async () => {
      // Test with expired timestamp
      const expiredCode = {
        code: 'EXPIRED123',
        orderId: 'test-order',
        createdAt: new Date(Date.now() - 11 * 60 * 1000), // 11 minutes ago
        expiresAt: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
        used: false
      };

      // This should be rejected by expiration check
      const isExpired = expiredCode.expiresAt < new Date();
      expect(isExpired).toBe(true);
    });
  });
});

// Helper functions
async function createTestUsers() {
  const users = [];
  
  for (let i = 0; i < 3; i++) {
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth, 
        `testuser${i}@university.edu`, 
        'testpassword123'
      );
      users.push(userCredential.user);
    } catch (error) {
      console.warn(`Failed to create test user ${i}:`, error);
    }
  }
  
  return users;
}

async function cleanupTestData() {
  // Cleanup would be handled by Firebase emulator reset
  console.log('Test cleanup completed');
}
