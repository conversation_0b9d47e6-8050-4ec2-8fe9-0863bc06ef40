"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.testStripeConnectReal = exports.createStripeConnectAccount = exports.getPaymentDetails = exports.getSellerPendingPayouts = exports.getStripeConnectAccountStatus = void 0;
// Real Stripe Connect functions with proper API integration
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const stripe_1 = __importDefault(require("stripe"));
// Initialize Firebase Admin
if (!admin.apps.length) {
    admin.initializeApp();
}
// Initialize Stripe
const stripe = new stripe_1.default(((_a = functions.config().stripe) === null || _a === void 0 ? void 0 : _a.api_key) || process.env.STRIPE_API_KEY || '', {
    apiVersion: '2025-05-28.basil',
});
console.log('🚀 Real Stripe Connect Functions loading...');
// Get Stripe Connect account status
exports.getStripeConnectAccountStatus = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log('Fetching Stripe Connect account status for user:', userId);
        // Get connect account from Firestore
        const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (!connectAccountDoc.exists) {
            console.log(`No connect account found for user: ${userId}`);
            return null;
        }
        const connectAccount = connectAccountDoc.data();
        const stripeAccountId = connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.stripeAccountId;
        if (!stripeAccountId) {
            console.log(`No Stripe account ID found for user: ${userId}`);
            return null;
        }
        // Fetch real-time data from Stripe
        let stripeAccount;
        try {
            stripeAccount = await stripe.accounts.retrieve(stripeAccountId);
            console.log('Fetched Stripe account data:', stripeAccount.id);
        }
        catch (stripeError) {
            console.error('Error fetching Stripe account:', stripeError);
            // Return cached data if Stripe API fails
            return {
                accountId: stripeAccountId,
                onboardingUrl: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.onboardingUrl) || null,
                dashboardUrl: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.dashboardUrl) || null,
                isOnboarded: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.isOnboarded) || false,
                chargesEnabled: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.chargesEnabled) || false,
                payoutsEnabled: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.payoutsEnabled) || false,
            };
        }
        // Update Firestore with latest Stripe data
        const isOnboarded = stripeAccount.details_submitted && stripeAccount.charges_enabled;
        const updateData = {
            isOnboarded,
            chargesEnabled: stripeAccount.charges_enabled || false,
            payoutsEnabled: stripeAccount.payouts_enabled || false,
            detailsSubmitted: stripeAccount.details_submitted || false,
            updatedAt: admin.firestore.Timestamp.now(),
        };
        // Add dashboard URL if account is onboarded
        if (isOnboarded && !(connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.dashboardUrl)) {
            try {
                const loginLink = await stripe.accounts.createLoginLink(stripeAccountId);
                updateData.dashboardUrl = loginLink.url;
            }
            catch (loginError) {
                console.error('Error creating login link:', loginError);
            }
        }
        await admin.firestore().collection('connectAccounts').doc(userId).update(updateData);
        return {
            accountId: stripeAccountId,
            onboardingUrl: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.onboardingUrl) || null,
            dashboardUrl: updateData.dashboardUrl || (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.dashboardUrl) || null,
            isOnboarded,
            chargesEnabled: stripeAccount.charges_enabled || false,
            payoutsEnabled: stripeAccount.payouts_enabled || false,
        };
    }
    catch (error) {
        console.error('Error in getStripeConnectAccountStatus:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get pending payouts for seller
exports.getSellerPendingPayouts = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log('Fetching pending payouts for user:', userId);
        let pendingPayouts = [];
        try {
            // Get orders where this user is the seller and payment has succeeded but funds not released
            const ordersQuery = await admin.firestore()
                .collection('orders')
                .where('sellerId', '==', userId)
                .where('status', 'in', ['payment_succeeded', 'delivered'])
                .where('fundsReleased', '==', false)
                .get();
            console.log(`Found ${ordersQuery.docs.length} pending orders for seller ${userId}`);
            pendingPayouts = ordersQuery.docs.map(doc => {
                const orderData = doc.data();
                const totalAmount = orderData.totalAmount || 0;
                // Calculate commission (8% for textbooks, 10% for others, $0.50 flat fee for items $1-$5)
                let commissionAmount = 0;
                if (totalAmount <= 5) {
                    commissionAmount = 0.50;
                }
                else {
                    const commissionRate = orderData.category === 'textbooks' ? 0.08 : 0.10;
                    commissionAmount = totalAmount * commissionRate;
                }
                const sellerAmount = Math.max(0, totalAmount - commissionAmount);
                return {
                    orderId: doc.id,
                    amount: totalAmount,
                    commissionAmount: commissionAmount,
                    sellerAmount: sellerAmount,
                    paymentIntentId: orderData.paymentIntentId || null,
                    createdAt: orderData.createdAt || orderData.paymentCompletedAt,
                    status: orderData.status,
                    listingTitle: orderData.listingTitle || orderData.title,
                    buyerName: orderData.buyerName || 'Anonymous'
                };
            });
            console.log(`Processed ${pendingPayouts.length} pending payouts`);
        }
        catch (firestoreError) {
            console.error('Error querying Firestore for pending payouts:', firestoreError);
            // Return empty array instead of throwing error
            pendingPayouts = [];
        }
        return pendingPayouts;
    }
    catch (error) {
        console.error('Error in getSellerPendingPayouts:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get detailed payment information from Stripe
exports.getPaymentDetails = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a, _b;
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get the order from Firestore
        const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify user has access to this order
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== context.auth.uid && (orderData === null || orderData === void 0 ? void 0 : orderData.sellerId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Access denied');
        }
        const stripe = new stripe_1.default(functions.config().stripe.secret_key, {
            apiVersion: '2025-05-28.basil',
        });
        const paymentDetails = {
            orderId: orderId,
            orderAmount: (orderData === null || orderData === void 0 ? void 0 : orderData.amount) || 0,
            commissionAmount: (orderData === null || orderData === void 0 ? void 0 : orderData.commissionAmount) || 0,
            sellerAmount: (orderData === null || orderData === void 0 ? void 0 : orderData.sellerAmount) || 0,
            walletAmountUsed: (orderData === null || orderData === void 0 ? void 0 : orderData.walletAmountUsed) || 0,
            status: (orderData === null || orderData === void 0 ? void 0 : orderData.status) || 'unknown',
            createdAt: orderData === null || orderData === void 0 ? void 0 : orderData.createdAt,
            paymentCompletedAt: orderData === null || orderData === void 0 ? void 0 : orderData.paymentCompletedAt,
        };
        // Fetch Stripe Payment Intent details if available
        if (orderData === null || orderData === void 0 ? void 0 : orderData.stripePaymentIntentId) {
            try {
                const paymentIntent = await stripe.paymentIntents.retrieve(orderData.stripePaymentIntentId, {
                    expand: ['charges', 'payment_method']
                });
                paymentDetails.stripePaymentIntent = {
                    id: paymentIntent.id,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    status: paymentIntent.status,
                    created: paymentIntent.created,
                    description: paymentIntent.description,
                    receipt_email: paymentIntent.receipt_email,
                    payment_method_types: paymentIntent.payment_method_types,
                };
                // Get payment method details if available
                if (paymentIntent.payment_method && typeof paymentIntent.payment_method === 'object') {
                    const paymentMethod = paymentIntent.payment_method;
                    paymentDetails.paymentMethod = {
                        type: paymentMethod.type,
                        card: paymentMethod.card ? {
                            brand: paymentMethod.card.brand,
                            last4: paymentMethod.card.last4,
                            exp_month: paymentMethod.card.exp_month,
                            exp_year: paymentMethod.card.exp_year,
                            funding: paymentMethod.card.funding,
                        } : null,
                    };
                }
                // Get charges information separately
                try {
                    const charges = await stripe.charges.list({
                        payment_intent: paymentIntent.id,
                        limit: 1
                    });
                    if (charges.data.length > 0) {
                        const charge = charges.data[0];
                        paymentDetails.charge = {
                            id: charge.id,
                            amount: charge.amount,
                            currency: charge.currency,
                            status: charge.status,
                            created: charge.created,
                            paid: charge.paid,
                            refunded: charge.refunded,
                            amount_refunded: charge.amount_refunded,
                            receipt_url: charge.receipt_url,
                            billing_details: charge.billing_details,
                            outcome: charge.outcome,
                        };
                    }
                }
                catch (chargeError) {
                    console.error('Error fetching charges:', chargeError);
                }
            }
            catch (stripeError) {
                console.error('Error fetching Stripe payment intent:', stripeError);
                paymentDetails.stripeError = 'Unable to fetch payment details from Stripe';
            }
        }
        // Fetch Stripe Session details if available
        if (orderData === null || orderData === void 0 ? void 0 : orderData.stripeSessionId) {
            try {
                const session = await stripe.checkout.sessions.retrieve(orderData.stripeSessionId);
                paymentDetails.stripeSession = {
                    id: session.id,
                    payment_status: session.payment_status,
                    status: session.status,
                    amount_total: session.amount_total,
                    currency: session.currency,
                    created: session.created,
                    expires_at: session.expires_at,
                    customer_email: (_a = session.customer_details) === null || _a === void 0 ? void 0 : _a.email,
                    customer_name: (_b = session.customer_details) === null || _b === void 0 ? void 0 : _b.name,
                    payment_method_types: session.payment_method_types,
                };
            }
            catch (stripeError) {
                console.error('Error fetching Stripe session:', stripeError);
                paymentDetails.sessionError = 'Unable to fetch session details from Stripe';
            }
        }
        return paymentDetails;
    }
    catch (error) {
        console.error('Error in getPaymentDetails:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Create Stripe Connect account
exports.createStripeConnectAccount = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { accountType } = data;
        const userId = context.auth.uid;
        if (!accountType || !['student', 'merchant'].includes(accountType)) {
            throw new functions.https.HttpsError('invalid-argument', 'Valid accountType is required');
        }
        console.log(`Creating Stripe Connect account for user ${userId}, type: ${accountType}`);
        // Check if user already has a connect account
        const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (existingAccountDoc.exists) {
            const existingAccount = existingAccountDoc.data();
            console.log('User already has a connect account:', existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.stripeAccountId);
            return {
                accountId: existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.stripeAccountId,
                onboardingUrl: (existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.onboardingUrl) || null,
                message: 'Account already exists'
            };
        }
        // Get user data
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User profile not found');
        }
        const userData = userDoc.data();
        // Create a real Stripe Connect Express account
        const account = await stripe.accounts.create({
            type: 'express',
            country: 'US',
            email: userData === null || userData === void 0 ? void 0 : userData.email,
            capabilities: {
                card_payments: { requested: true },
                transfers: { requested: true },
            },
            business_type: accountType === 'merchant' ? 'company' : 'individual',
            metadata: {
                userId,
                accountType,
                platform: 'hive_campus'
            }
        });
        console.log(`Created Stripe Connect account ${account.id} for user ${userId}`);
        // Create account link for onboarding
        const accountLink = await stripe.accountLinks.create({
            account: account.id,
            refresh_url: `${process.env.APP_URL || 'https://h1c1-798a8.web.app'}/settings/payment?refresh=true`,
            return_url: `${process.env.APP_URL || 'https://h1c1-798a8.web.app'}/settings/payment?success=true`,
            type: 'account_onboarding',
        });
        // Store the Connect account in Firestore
        await admin.firestore().collection('connectAccounts').doc(userId).set({
            userId: userId,
            stripeAccountId: account.id,
            accountType: accountType,
            isOnboarded: false,
            chargesEnabled: account.charges_enabled || false,
            payoutsEnabled: account.payouts_enabled || false,
            detailsSubmitted: account.details_submitted || false,
            onboardingUrl: accountLink.url,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
        });
        console.log(`Stored Connect account ${account.id} in Firestore for user ${userId}`);
        return {
            accountId: account.id,
            onboardingUrl: accountLink.url,
            message: 'Connect account created successfully'
        };
    }
    catch (error) {
        console.error('Error in createStripeConnectAccount:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Test function to verify deployment
exports.testStripeConnectReal = functions
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Real Stripe Connect functions deployed successfully',
        timestamp: new Date().toISOString(),
        functions: [
            'getStripeConnectAccountStatus',
            'getSellerPendingPayouts',
            'createStripeConnectAccount'
        ]
    });
});
