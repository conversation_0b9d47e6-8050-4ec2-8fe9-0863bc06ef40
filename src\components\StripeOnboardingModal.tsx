import React, { useState, useEffect } from 'react';
import {
  X,
  CreditCard,
  Shield,
  Zap,
  CheckCircle,
  ArrowRight,
  Loader,
  AlertCircle,
  DollarSign,
  Clock
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useStripeConnect } from '../hooks/useStripeConnect';

interface StripeOnboardingModalProps {
  isOpen: boolean;
  onClose: () => void;
  pendingAmount?: number;
  orderCount?: number;
  onOnboardingComplete?: () => void;
}

const StripeOnboardingModal: React.FC<StripeOnboardingModalProps> = ({
  isOpen,
  onClose,
  pendingAmount = 0,
  orderCount = 0,
  onOnboardingComplete
}) => {
  const { userProfile } = useAuth();
  const { createAccount, getOnboardingLink, accountStatus } = useStripeConnect();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'intro' | 'account-type' | 'processing' | 'success'>('intro');
  const [onboardingUrl, setOnboardingUrl] = useState<string | null>(null);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setStep('intro');
      setError(null);
      setOnboardingUrl(null);
    }
  }, [isOpen]);

  const handleCreateAccount = async (accountType: 'student' | 'merchant') => {
    if (!userProfile) return;

    setIsLoading(true);
    setError(null);
    setStep('processing');

    try {
      // Use the Firebase Callable function instead of direct fetch
      const data = await createAccount(accountType);
      console.log('Account creation response:', data);
      console.log('Setting onboarding URL:', data.onboardingUrl);
      setOnboardingUrl(data.onboardingUrl);

      // Immediately open the Stripe URL in a new tab
      if (data.onboardingUrl && (data.onboardingUrl.includes('connect.stripe.com') || data.onboardingUrl.includes('stripe.com'))) {
        console.log('🚀 Opening Stripe onboarding URL immediately:', data.onboardingUrl);
        const opened = window.open(data.onboardingUrl, '_blank', 'noopener,noreferrer');

        if (opened) {
          console.log('✅ Stripe onboarding opened successfully in new tab');
          setStep('success');
        } else {
          console.error('❌ Failed to open Stripe URL - popup might be blocked');
          setError('Popup blocked! Please allow popups for this site and try again, or click the button below to open Stripe manually.');
          setStep('success'); // Still show success step with manual option
        }
      } else {
        console.error('❌ Invalid Stripe URL received:', data.onboardingUrl);
        setError('Invalid Stripe setup URL received. Please try again.');
        setStep('intro');
      }
    } catch (error) {
      console.error('Error creating Stripe account:', error);

      // Check for various error types and provide helpful messages
      const errorMessage = error instanceof Error ? error.message : 'Failed to create account';

      if (errorMessage.includes('already has a fully onboarded Connect account')) {
        setError('Your payment account is already set up! You can start receiving payments immediately.');
        setStep('intro');
      } else if (errorMessage.includes('Unexpected token') || errorMessage.includes('<!doctype') ||
          errorMessage.includes('500') || errorMessage.includes('internal')) {
        setError('Payment setup is temporarily unavailable. Please try again in a moment.');
        setStep('intro');
      } else if (errorMessage.includes('signed up for Connect')) {
        setError('Payment processing setup is being configured. You can continue using the platform normally.');
        setStep('intro');
      } else if (errorMessage.includes('functions/internal')) {
        setError('Payment system is being updated. All other features work normally - you can create listings and browse items!');
        setStep('intro');
      } else {
        setError(`Failed to set up payment account: ${errorMessage}`);
        setStep('intro');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinueToStripe = () => {
    if (onboardingUrl) {
      console.log('Opening Stripe onboarding URL:', onboardingUrl);
      // Ensure we're opening a valid Stripe URL
      if (onboardingUrl.includes('connect.stripe.com') || onboardingUrl.includes('stripe.com')) {
        // Try multiple methods to open the URL
        try {
          // Method 1: window.open with specific parameters
          const newWindow = window.open(onboardingUrl, '_blank', 'noopener,noreferrer,width=800,height=600');

          if (newWindow) {
            console.log('✅ Stripe onboarding opened in new tab. Modal will stay open.');
            // Focus the new window
            newWindow.focus();
          } else {
            // Method 2: Try with location.href in new window
            console.log('🔄 Trying alternative method to open Stripe URL...');
            const altWindow = window.open('', '_blank');
            if (altWindow) {
              altWindow.location.href = onboardingUrl;
              altWindow.focus();
              console.log('✅ Stripe onboarding opened using alternative method.');
            } else {
              // Method 3: Create a temporary link and click it
              console.log('🔄 Trying link click method...');
              const link = document.createElement('a');
              link.href = onboardingUrl;
              link.target = '_blank';
              link.rel = 'noopener noreferrer';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              console.log('✅ Stripe onboarding opened using link click method.');
            }
          }
        } catch (error) {
          console.error('❌ All methods failed to open Stripe URL:', error);
          setError('Unable to open Stripe automatically. Please copy the URL below and open it manually.');
        }
      } else {
        console.error('Invalid Stripe URL:', onboardingUrl);
        setError('Invalid Stripe setup URL. Please try again.');
      }
    } else {
      console.error('No onboarding URL available');
      setError('No setup URL available. Please try creating your account again.');
    }
  };

  const handleCompleteExistingSetup = async () => {
    if (!accountStatus) return;

    setIsLoading(true);
    setError(null);

    try {
      const baseUrl = window.location.origin;

      // First try the Firebase function
      try {
        const { onboardingUrl: freshUrl } = await getOnboardingLink(
          `${baseUrl}/profile?stripe_refresh=true`,
          `${baseUrl}/profile?stripe_success=true`
        );

        if (freshUrl && (freshUrl.includes('connect.stripe.com') || freshUrl.includes('stripe.com'))) {
          window.open(freshUrl, '_blank', 'noopener,noreferrer');
          console.log('✅ Existing setup opened in new tab. Modal will stay open.');
          setStep('success');
          return;
        }
      } catch (functionError) {
        console.warn('Firebase function failed, trying fallback:', functionError);
      }

      // Fallback: Use direct Stripe Connect URL if we have an account ID
      if (accountStatus.accountId) {
        const fallbackUrl = `https://connect.stripe.com/setup/s/${accountStatus.accountId}`;
        window.open(fallbackUrl, '_blank', 'noopener,noreferrer');
        console.log('✅ Fallback setup opened in new tab. Modal will stay open.');
        setStep('success');
      } else {
        throw new Error('No account ID available for Stripe setup');
      }

    } catch (error: unknown) {
      console.error('Error getting onboarding link:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to get setup link';
      setError(`Unable to open Stripe setup: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {step === 'intro' && 'Set Up Payouts'}
            {step === 'account-type' && 'Choose Account Type'}
            {step === 'processing' && 'Creating Account...'}
            {step === 'success' && 'Almost Done!'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'intro' && (
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="w-8 h-8 text-white" />
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                You're just one step away from getting paid!
              </h3>
              
              {pendingAmount > 0 && (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <Clock className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <span className="font-semibold text-green-800 dark:text-green-200">
                      ${pendingAmount.toFixed(2)} waiting for you
                    </span>
                  </div>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    From {orderCount} recent {orderCount === 1 ? 'sale' : 'sales'}
                  </p>
                </div>
              )}
              
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Set up payouts with Stripe to receive payments directly to your bank account.
                It's quick, secure, and required by financial regulations.
              </p>

              {error && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/40 rounded-full flex items-center justify-center mt-0.5">
                      <AlertCircle className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm text-blue-800 dark:text-blue-200 font-medium mb-1">
                        Payment Setup Status
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        {error}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Benefits */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-3 text-left">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                    <Zap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Setup takes less than 2 minutes
                  </span>
                </div>
                
                <div className="flex items-center space-x-3 text-left">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                    <Shield className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Bank-level security with Stripe
                  </span>
                </div>
                
                <div className="flex items-center space-x-3 text-left">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                    <CreditCard className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Automatic payments to your bank
                  </span>
                </div>
              </div>

              <button
                onClick={() => setStep('account-type')}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-3 px-6 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2 mb-3"
              >
                <span>Set Up New Account</span>
                <ArrowRight className="w-5 h-5" />
              </button>

              {/* Complete existing setup button for users who already have a Stripe account */}
              {accountStatus && accountStatus.accountId && !accountStatus.isOnboarded && (
                <button
                  onClick={handleCompleteExistingSetup}
                  disabled={isLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-6 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2 mb-3"
                >
                  {isLoading ? (
                    <>
                      <Loader className="w-5 h-5 animate-spin" />
                      <span>Opening Stripe...</span>
                    </>
                  ) : (
                    <>
                      <span>Complete Existing Setup</span>
                      <ArrowRight className="w-5 h-5" />
                    </>
                  )}
                </button>
              )}

              <button
                onClick={onClose}
                className="w-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-6 rounded-xl font-medium transition-all"
              >
                Skip for Now
              </button>

              <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-3">
                You can set up payouts later from your profile settings. You can still create listings without payment setup.
              </p>
            </div>
          )}

          {step === 'account-type' && (
            <div>
              <p className="text-gray-600 dark:text-gray-400 mb-6 text-center">
                Choose the account type that best describes you:
              </p>
              
              <div className="space-y-4">
                <button
                  onClick={() => handleCreateAccount('student')}
                  disabled={isLoading}
                  className="w-full p-4 border-2 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 rounded-xl transition-all text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">Student Seller</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        For individual students selling personal items
                      </p>
                    </div>
                  </div>
                </button>
                
                <button
                  onClick={() => handleCreateAccount('merchant')}
                  disabled={isLoading}
                  className="w-full p-4 border-2 border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600 rounded-xl transition-all text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                      <Shield className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">Merchant Partner</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        For campus businesses and organizations
                      </p>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <Loader className="w-12 h-12 text-blue-600 dark:text-blue-400 animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Creating your account...
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                This will only take a moment
              </p>
            </div>
          )}

          {step === 'success' && (
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>

              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Stripe Setup Ready!
              </h3>

              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {error ?
                  "Complete your setup with Stripe to start receiving payments." :
                  "We've opened Stripe in a new tab. If it didn't open automatically, click below."
                }
              </p>

              <div className="space-y-3">
                {onboardingUrl && (
                  <>
                    <button
                      onClick={handleContinueToStripe}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2"
                    >
                      <span>Open Stripe Setup</span>
                      <ArrowRight className="w-4 h-4" />
                    </button>

                    <div className="text-center">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                        If the button doesn't work, copy this URL:
                      </p>
                      <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-lg">
                        <input
                          type="text"
                          value={onboardingUrl}
                          readOnly
                          className="w-full text-xs bg-transparent border-none outline-none text-center text-gray-700 dark:text-gray-300"
                          onClick={(e) => {
                            e.currentTarget.select();
                            navigator.clipboard.writeText(onboardingUrl);
                          }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Click to copy, then paste in a new browser tab
                      </p>
                    </div>
                  </>
                )}

                <button
                  onClick={onClose}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-xl font-semibold transition-all"
                >
                  Close This Window
                </button>

                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Your progress will be saved automatically
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800 dark:text-red-200">Error</p>
                  <p className="text-sm text-red-700 dark:text-red-300 mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StripeOnboardingModal;
