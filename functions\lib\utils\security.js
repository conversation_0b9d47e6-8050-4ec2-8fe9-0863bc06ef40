"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityConfig = exports.InputSanitizer = exports.RateLimiter = exports.SecureCodeGenerator = exports.SecurePinManager = void 0;
const admin = __importStar(require("firebase-admin"));
const bcrypt = __importStar(require("bcrypt"));
const crypto_1 = require("crypto");
const functions = __importStar(require("firebase-functions/v1"));
// Security utilities for Hive Campus
// Constants for security configuration
const BCRYPT_ROUNDS = 12; // High security rounds for bcrypt
const PIN_LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes lockout
const MAX_PIN_ATTEMPTS = 5;
const SECRET_CODE_EXPIRY = 10 * 60 * 1000; // 10 minutes for secret codes
/**
 * Secure PIN management with bcrypt and salt
 */
class SecurePinManager {
    /**
     * Hash a PIN using bcrypt with salt
     */
    static async hashPin(pin) {
        try {
            // Add application-specific salt before hashing
            const saltedPin = pin + process.env.HIVE_PIN_SALT || 'HIVE_CAMPUS_2024_SECURE';
            return await bcrypt.hash(saltedPin, BCRYPT_ROUNDS);
        }
        catch (error) {
            console.error('Error hashing PIN:', error);
            throw new functions.https.HttpsError('internal', 'Failed to secure PIN');
        }
    }
    /**
     * Verify a PIN against stored hash
     */
    static async verifyPin(pin, hashedPin) {
        try {
            const saltedPin = pin + process.env.HIVE_PIN_SALT || 'HIVE_CAMPUS_2024_SECURE';
            return await bcrypt.compare(saltedPin, hashedPin);
        }
        catch (error) {
            console.error('Error verifying PIN:', error);
            return false;
        }
    }
    /**
     * Check and update rate limiting for PIN attempts
     */
    static async checkPinRateLimit(userId, clientIP) {
        const identifier = `pin_${userId}_${clientIP || 'unknown'}`;
        const rateLimitRef = admin.firestore().collection('rateLimits').doc(identifier);
        try {
            const result = await admin.firestore().runTransaction(async (transaction) => {
                const doc = await transaction.get(rateLimitRef);
                const now = admin.firestore.Timestamp.now();
                if (!doc.exists) {
                    // First attempt
                    transaction.set(rateLimitRef, {
                        count: 1,
                        firstAttempt: now,
                        lastAttempt: now
                    });
                    return true;
                }
                const data = doc.data();
                // Check if currently locked out
                if (data.lockedUntil && data.lockedUntil.toMillis() > now.toMillis()) {
                    return false; // Still locked out
                }
                // Reset if window has passed (1 hour)
                const windowStart = now.toMillis() - (60 * 60 * 1000);
                if (data.firstAttempt.toMillis() < windowStart) {
                    transaction.set(rateLimitRef, {
                        count: 1,
                        firstAttempt: now,
                        lastAttempt: now
                    });
                    return true;
                }
                // Check if max attempts exceeded
                if (data.count >= MAX_PIN_ATTEMPTS) {
                    const lockoutUntil = admin.firestore.Timestamp.fromMillis(now.toMillis() + PIN_LOCKOUT_DURATION);
                    transaction.update(rateLimitRef, {
                        lockedUntil: lockoutUntil,
                        lastAttempt: now
                    });
                    return false;
                }
                // Increment attempt count
                transaction.update(rateLimitRef, {
                    count: data.count + 1,
                    lastAttempt: now
                });
                return true;
            });
            return result;
        }
        catch (error) {
            console.error('Error checking PIN rate limit:', error);
            // Fail secure - deny access on error
            return false;
        }
    }
    /**
     * Reset rate limiting after successful PIN verification
     */
    static async resetPinRateLimit(userId, clientIP) {
        const identifier = `pin_${userId}_${clientIP || 'unknown'}`;
        const rateLimitRef = admin.firestore().collection('rateLimits').doc(identifier);
        try {
            await rateLimitRef.delete();
        }
        catch (error) {
            console.error('Error resetting PIN rate limit:', error);
            // Non-critical error, don't throw
        }
    }
}
exports.SecurePinManager = SecurePinManager;
/**
 * Secure code generation for delivery confirmation
 */
class SecureCodeGenerator {
    /**
     * Generate cryptographically secure 6-digit hex code
     */
    static generateSecretCode() {
        return (0, crypto_1.randomBytes)(3).toString('hex').toUpperCase();
    }
    /**
     * Store secret code with expiration
     */
    static async storeSecretCode(orderId, code) {
        const expiresAt = admin.firestore.Timestamp.fromMillis(Date.now() + SECRET_CODE_EXPIRY);
        await admin.firestore().collection('secretCodes').doc(orderId).set({
            code,
            orderId,
            createdAt: admin.firestore.Timestamp.now(),
            expiresAt,
            used: false
        });
    }
    /**
     * Verify and consume secret code
     */
    static async verifySecretCode(orderId, providedCode) {
        const codeRef = admin.firestore().collection('secretCodes').doc(orderId);
        try {
            const result = await admin.firestore().runTransaction(async (transaction) => {
                var _a;
                const doc = await transaction.get(codeRef);
                if (!doc.exists) {
                    return false;
                }
                const data = doc.data();
                const now = admin.firestore.Timestamp.now();
                // Check if expired
                if ((data === null || data === void 0 ? void 0 : data.expiresAt) && data.expiresAt.toMillis() < now.toMillis()) {
                    return false;
                }
                // Check if already used
                if (data === null || data === void 0 ? void 0 : data.used) {
                    return false;
                }
                // Check if code matches (case-insensitive)
                if (((_a = data === null || data === void 0 ? void 0 : data.code) === null || _a === void 0 ? void 0 : _a.toUpperCase()) !== providedCode.toUpperCase()) {
                    return false;
                }
                // Mark as used
                transaction.update(codeRef, {
                    used: true,
                    usedAt: now
                });
                return true;
            });
            return result;
        }
        catch (error) {
            console.error('Error verifying secret code:', error);
            return false;
        }
    }
}
exports.SecureCodeGenerator = SecureCodeGenerator;
/**
 * General rate limiting utility
 */
class RateLimiter {
    /**
     * Check rate limit for any action
     */
    static async checkRateLimit(identifier, action, maxAttempts = 10, windowMs = 15 * 60 * 1000 // 15 minutes
    ) {
        const rateLimitId = `${action}_${identifier}`;
        const rateLimitRef = admin.firestore().collection('rateLimits').doc(rateLimitId);
        try {
            const result = await admin.firestore().runTransaction(async (transaction) => {
                const doc = await transaction.get(rateLimitRef);
                const now = admin.firestore.Timestamp.now();
                if (!doc.exists) {
                    transaction.set(rateLimitRef, {
                        count: 1,
                        firstAttempt: now,
                        lastAttempt: now
                    });
                    return true;
                }
                const data = doc.data();
                const windowStart = now.toMillis() - windowMs;
                // Reset if window has passed
                if (data.firstAttempt.toMillis() < windowStart) {
                    transaction.set(rateLimitRef, {
                        count: 1,
                        firstAttempt: now,
                        lastAttempt: now
                    });
                    return true;
                }
                // Check if max attempts exceeded
                if (data.count >= maxAttempts) {
                    return false;
                }
                // Increment count
                transaction.update(rateLimitRef, {
                    count: data.count + 1,
                    lastAttempt: now
                });
                return true;
            });
            return result;
        }
        catch (error) {
            console.error('Error checking rate limit:', error);
            return false; // Fail secure
        }
    }
}
exports.RateLimiter = RateLimiter;
/**
 * Input sanitization utilities
 */
class InputSanitizer {
    /**
     * Remove HTML tags and dangerous characters
     */
    static sanitizeText(input) {
        return input
            .replace(/<[^>]*>/g, '') // Remove HTML tags
            .replace(/javascript:/gi, '') // Remove javascript: URLs
            .replace(/on\w+\s*=/gi, '') // Remove event handlers
            .trim();
    }
    /**
     * Sanitize and validate email
     */
    static sanitizeEmail(email) {
        return email.toLowerCase().trim();
    }
    /**
     * Validate and sanitize numeric input
     */
    static sanitizeNumber(input, min = 0, max = Number.MAX_SAFE_INTEGER) {
        const num = Number(input);
        if (isNaN(num) || num < min || num > max) {
            throw new Error(`Invalid number: must be between ${min} and ${max}`);
        }
        return num;
    }
}
exports.InputSanitizer = InputSanitizer;
// Export security configuration constants
exports.SecurityConfig = {
    BCRYPT_ROUNDS,
    PIN_LOCKOUT_DURATION,
    MAX_PIN_ATTEMPTS,
    SECRET_CODE_EXPIRY
};
