"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendBatchNotifications = exports.sendNotification = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Default notification preferences
const DEFAULT_PREFERENCES = {
    enable_push: true,
    enable_email: true,
    pause_all: false,
    muted_categories: [],
    channels: {
        in_app: {
            enabled: true,
            types: [
                'listing_sold', 'order_confirmed', 'order_delivered', 'wallet_credited',
                'wallet_debited', 'new_chat_message', '48_hour_shipping_reminder',
                'platform_announcement', 'auction_update', 'payment_failed', 'user_warning',
                'delivery_confirmation', 'admin_warning', 'admin_broadcast', 'payment_success'
            ]
        },
        push: {
            enabled: true,
            types: [
                'listing_sold', 'order_confirmed', 'order_delivered', 'wallet_credited',
                'new_chat_message', '48_hour_shipping_reminder', 'payment_failed',
                'user_warning', 'delivery_confirmation', 'admin_warning'
            ]
        },
        email: {
            enabled: true,
            types: [
                'wallet_credited', 'order_confirmed', '48_hour_shipping_reminder',
                'user_warning', 'payment_failed', 'payment_success'
            ]
        }
    }
};
// Get user notification preferences
async function getUserPreferences(userId) {
    var _a;
    try {
        const userDoc = await admin.firestore().doc(`users/${userId}`).get();
        if (userDoc.exists) {
            const userData = userDoc.data();
            const preferences = (_a = userData === null || userData === void 0 ? void 0 : userData.preferences) === null || _a === void 0 ? void 0 : _a.notifications;
            if (preferences) {
                return Object.assign(Object.assign(Object.assign({}, DEFAULT_PREFERENCES), preferences), { channels: Object.assign(Object.assign({}, DEFAULT_PREFERENCES.channels), preferences.channels) });
            }
        }
        return DEFAULT_PREFERENCES;
    }
    catch (error) {
        console.error('Error getting user preferences:', error);
        return DEFAULT_PREFERENCES;
    }
}
// Check if notification should be sent based on preferences
function shouldSendNotification(type, channel, preferences) {
    // Check if all notifications are paused
    if (preferences.pause_all) {
        return false;
    }
    // Check if category is muted
    if (preferences.muted_categories.includes(type)) {
        return false;
    }
    // Check channel-specific settings
    const channelPrefs = preferences.channels[channel];
    if (!channelPrefs.enabled || !channelPrefs.types.includes(type)) {
        return false;
    }
    // Check global channel settings
    if (channel === 'push' && !preferences.enable_push) {
        return false;
    }
    if (channel === 'email' && !preferences.enable_email) {
        return false;
    }
    return true;
}
// Create in-app notification
async function createInAppNotification(request) {
    try {
        const notificationData = {
            type: request.type,
            title: request.title,
            message: request.message,
            icon: request.icon || '/icons/icon-192.png',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            read: false,
            link: request.link,
            priority: request.priority || 'normal',
            actionRequired: request.actionRequired || false,
            expiresAt: request.expiresAt,
            metadata: request.metadata || {},
            orderId: request.orderId,
            listingId: request.listingId,
            chatId: request.chatId,
            senderId: request.senderId,
            amount: request.amount,
            secretCode: request.secretCode
        };
        await admin.firestore()
            .collection(`users/${request.userId}/notifications`)
            .add(notificationData);
        console.log(`In-app notification created for user ${request.userId}`);
    }
    catch (error) {
        console.error('Error creating in-app notification:', error);
        throw error;
    }
}
// Send push notification
async function sendPushNotification(request) {
    try {
        // Get user's FCM token
        const tokenDoc = await admin.firestore()
            .doc(`users/${request.userId}/fcmTokens/web`)
            .get();
        if (!tokenDoc.exists) {
            console.log(`No FCM token found for user ${request.userId}`);
            return;
        }
        const tokenData = tokenDoc.data();
        if (!(tokenData === null || tokenData === void 0 ? void 0 : tokenData.active) || !(tokenData === null || tokenData === void 0 ? void 0 : tokenData.token)) {
            console.log(`Inactive FCM token for user ${request.userId}`);
            return;
        }
        // Prepare push notification payload
        const payload = {
            token: tokenData.token,
            notification: {
                title: request.title,
                body: request.message,
                imageUrl: request.icon
            },
            data: {
                type: request.type,
                link: request.link || '/',
                orderId: request.orderId || '',
                listingId: request.listingId || '',
                chatId: request.chatId || '',
                requireInteraction: request.actionRequired ? 'true' : 'false'
            },
            webpush: {
                headers: {
                    TTL: '86400', // 24 hours
                    Urgency: request.priority === 'urgent' ? 'high' : 'normal'
                },
                notification: {
                    icon: request.icon || '/icons/icon-192.png',
                    badge: '/icons/icon-96.png',
                    tag: request.type,
                    requireInteraction: request.actionRequired || false,
                    actions: [
                        {
                            action: 'view',
                            title: 'View'
                        },
                        {
                            action: 'dismiss',
                            title: 'Dismiss'
                        }
                    ],
                    vibrate: [200, 100, 200]
                }
            }
        };
        // Send the notification
        await admin.messaging().send(payload);
        console.log(`Push notification sent to user ${request.userId}`);
    }
    catch (error) {
        console.error('Error sending push notification:', error);
        // If token is invalid, mark it as inactive
        if (error.code === 'messaging/registration-token-not-registered') {
            try {
                await admin.firestore()
                    .doc(`users/${request.userId}/fcmTokens/web`)
                    .update({ active: false });
            }
            catch (updateError) {
                console.error('Error updating invalid FCM token:', updateError);
            }
        }
    }
}
// Send email notification (placeholder - implement with your email service)
async function sendEmailNotification(request) {
    try {
        // Get user email
        const userDoc = await admin.firestore().doc(`users/${request.userId}`).get();
        if (!userDoc.exists) {
            console.log(`User ${request.userId} not found for email notification`);
            return;
        }
        const userData = userDoc.data();
        const email = userData === null || userData === void 0 ? void 0 : userData.email;
        if (!email) {
            console.log(`No email found for user ${request.userId}`);
            return;
        }
        // TODO: Implement email sending with your preferred service (SendGrid, Mailgun, etc.)
        console.log(`Email notification would be sent to ${email}:`, {
            subject: request.title,
            body: request.message,
            type: request.type
        });
        // For now, just log the email notification
        // In production, integrate with your email service here
    }
    catch (error) {
        console.error('Error sending email notification:', error);
    }
}
// Main function to send notifications
exports.sendNotification = functions.https.onCall(async (data, context) => {
    // Verify authentication
    if (!(context === null || context === void 0 ? void 0 : context.auth)) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const request = data;
    try {
        // Get user preferences
        const preferences = await getUserPreferences(request.userId);
        // Determine which channels to use
        const channels = request.channels || ['in_app', 'push', 'email'];
        const activeChannels = [];
        for (const channel of channels) {
            if (shouldSendNotification(request.type, channel, preferences)) {
                activeChannels.push(channel);
            }
        }
        if (activeChannels.length === 0) {
            console.log(`No active channels for notification type ${request.type} for user ${request.userId}`);
            return { success: true, channels: [] };
        }
        // Send notifications through active channels
        const promises = [];
        if (activeChannels.includes('in_app')) {
            promises.push(createInAppNotification(request));
        }
        if (activeChannels.includes('push')) {
            promises.push(sendPushNotification(request));
        }
        if (activeChannels.includes('email')) {
            promises.push(sendEmailNotification(request));
        }
        // Wait for all notifications to be sent
        await Promise.allSettled(promises);
        console.log(`Notifications sent for user ${request.userId} via channels:`, activeChannels);
        return { success: true, channels: activeChannels };
    }
    catch (error) {
        console.error('Error sending notification:', error);
        throw new functions.https.HttpsError('internal', 'Failed to send notification');
    }
});
// Batch notification function for multiple users
exports.sendBatchNotifications = functions.https.onCall(async (data, context) => {
    var _a;
    // Verify authentication and admin privileges
    if (!(context === null || context === void 0 ? void 0 : context.auth)) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const request = data;
    // Check if user is admin (you may want to implement proper admin check)
    const userDoc = await admin.firestore().doc(`users/${(_a = context === null || context === void 0 ? void 0 : context.auth) === null || _a === void 0 ? void 0 : _a.uid}`).get();
    const userData = userDoc.data();
    if (!(userData === null || userData === void 0 ? void 0 : userData.isAdmin)) {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can send batch notifications');
    }
    try {
        const { userIds, notification } = request;
        const results = [];
        // Send notifications to each user
        for (const userId of userIds) {
            try {
                const notificationRequest = Object.assign(Object.assign({}, notification), { userId });
                // Get user preferences
                const preferences = await getUserPreferences(userId);
                // Determine active channels
                const channels = notification.channels || ['in_app', 'push', 'email'];
                const activeChannels = [];
                for (const channel of channels) {
                    if (shouldSendNotification(notification.type, channel, preferences)) {
                        activeChannels.push(channel);
                    }
                }
                if (activeChannels.length > 0) {
                    const promises = [];
                    if (activeChannels.includes('in_app')) {
                        promises.push(createInAppNotification(notificationRequest));
                    }
                    if (activeChannels.includes('push')) {
                        promises.push(sendPushNotification(notificationRequest));
                    }
                    if (activeChannels.includes('email')) {
                        promises.push(sendEmailNotification(notificationRequest));
                    }
                    await Promise.allSettled(promises);
                }
                results.push({ userId, success: true });
            }
            catch (error) {
                console.error(`Error sending notification to user ${userId}:`, error);
                results.push({
                    userId,
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
        console.log(`Batch notifications sent to ${userIds.length} users`);
        return { success: true, results };
    }
    catch (error) {
        console.error('Error sending batch notifications:', error);
        throw new functions.https.HttpsError('internal', 'Failed to send batch notifications');
    }
});
