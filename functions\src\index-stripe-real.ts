// Real Stripe Connect functions with proper API integration
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import Strip<PERSON> from 'stripe';

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

// Initialize Stripe
const stripe = new Stripe(functions.config().stripe?.api_key || process.env.STRIPE_API_KEY || '', {
  apiVersion: '2025-05-28.basil',
});

console.log('🚀 Real Stripe Connect Functions loading...');

// Get Stripe Connect account status
export const getStripeConnectAccountStatus = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching Stripe Connect account status for user:', userId);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        console.log(`No connect account found for user: ${userId}`);
        return null;
      }

      const connectAccount = connectAccountDoc.data();
      const stripeAccountId = connectAccount?.stripeAccountId;

      if (!stripeAccountId) {
        console.log(`No Stripe account ID found for user: ${userId}`);
        return null;
      }

      // Fetch real-time data from Stripe
      let stripeAccount;
      try {
        stripeAccount = await stripe.accounts.retrieve(stripeAccountId);
        console.log('Fetched Stripe account data:', stripeAccount.id);
      } catch (stripeError) {
        console.error('Error fetching Stripe account:', stripeError);
        // Return cached data if Stripe API fails
        return {
          accountId: stripeAccountId,
          onboardingUrl: connectAccount?.onboardingUrl || null,
          dashboardUrl: connectAccount?.dashboardUrl || null,
          isOnboarded: connectAccount?.isOnboarded || false,
          chargesEnabled: connectAccount?.chargesEnabled || false,
          payoutsEnabled: connectAccount?.payoutsEnabled || false,
        };
      }

      // Update Firestore with latest Stripe data
      const isOnboarded = stripeAccount.details_submitted && stripeAccount.charges_enabled;
      const updateData: any = {
        isOnboarded,
        chargesEnabled: stripeAccount.charges_enabled || false,
        payoutsEnabled: stripeAccount.payouts_enabled || false,
        detailsSubmitted: stripeAccount.details_submitted || false,
        updatedAt: admin.firestore.Timestamp.now(),
      };

      // Add dashboard URL if account is onboarded
      if (isOnboarded && !connectAccount?.dashboardUrl) {
        try {
          const loginLink = await stripe.accounts.createLoginLink(stripeAccountId);
          updateData.dashboardUrl = loginLink.url;
        } catch (loginError) {
          console.error('Error creating login link:', loginError);
        }
      }

      await admin.firestore().collection('connectAccounts').doc(userId).update(updateData);

      return {
        accountId: stripeAccountId,
        onboardingUrl: connectAccount?.onboardingUrl || null,
        dashboardUrl: updateData.dashboardUrl || connectAccount?.dashboardUrl || null,
        isOnboarded,
        chargesEnabled: stripeAccount.charges_enabled || false,
        payoutsEnabled: stripeAccount.payouts_enabled || false,
      };
    } catch (error) {
      console.error('Error in getStripeConnectAccountStatus:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get pending payouts for seller
export const getSellerPendingPayouts = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching pending payouts for user:', userId);

      let pendingPayouts: any[] = [];

      try {
        // Get orders where this user is the seller and payment has succeeded but funds not released
        const ordersQuery = await admin.firestore()
          .collection('orders')
          .where('sellerId', '==', userId)
          .where('status', 'in', ['payment_succeeded', 'delivered'])
          .where('fundsReleased', '==', false)
          .get();

        console.log(`Found ${ordersQuery.docs.length} pending orders for seller ${userId}`);

        pendingPayouts = ordersQuery.docs.map(doc => {
          const orderData = doc.data();
          const totalAmount = orderData.totalAmount || 0;
          
          // Calculate commission (8% for textbooks, 10% for others, $0.50 flat fee for items $1-$5)
          let commissionAmount = 0;
          if (totalAmount <= 5) {
            commissionAmount = 0.50;
          } else {
            const commissionRate = orderData.category === 'textbooks' ? 0.08 : 0.10;
            commissionAmount = totalAmount * commissionRate;
          }
          
          const sellerAmount = Math.max(0, totalAmount - commissionAmount);

          return {
            orderId: doc.id,
            amount: totalAmount,
            commissionAmount: commissionAmount,
            sellerAmount: sellerAmount,
            paymentIntentId: orderData.paymentIntentId || null,
            createdAt: orderData.createdAt || orderData.paymentCompletedAt,
            status: orderData.status,
            listingTitle: orderData.listingTitle || orderData.title,
            buyerName: orderData.buyerName || 'Anonymous'
          };
        });

        console.log(`Processed ${pendingPayouts.length} pending payouts`);
        
      } catch (firestoreError) {
        console.error('Error querying Firestore for pending payouts:', firestoreError);
        // Return empty array instead of throwing error
        pendingPayouts = [];
      }

      return pendingPayouts;

    } catch (error) {
      console.error('Error in getSellerPendingPayouts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get detailed payment information from Stripe
export const getPaymentDetails = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { orderId } = data;
      if (!orderId) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
      }

      // Get the order from Firestore
      const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify user has access to this order
      if (orderData?.buyerId !== context.auth.uid && orderData?.sellerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Access denied');
      }

      const stripe = new Stripe(functions.config().stripe.secret_key, {
        apiVersion: '2025-05-28.basil',
      });

      const paymentDetails: any = {
        orderId: orderId,
        orderAmount: orderData?.amount || 0,
        commissionAmount: orderData?.commissionAmount || 0,
        sellerAmount: orderData?.sellerAmount || 0,
        walletAmountUsed: orderData?.walletAmountUsed || 0,
        status: orderData?.status || 'unknown',
        createdAt: orderData?.createdAt,
        paymentCompletedAt: orderData?.paymentCompletedAt,
      };

      // Fetch Stripe Payment Intent details if available
      if (orderData?.stripePaymentIntentId) {
        try {
          const paymentIntent = await stripe.paymentIntents.retrieve(orderData.stripePaymentIntentId, {
            expand: ['charges', 'payment_method']
          });

          paymentDetails.stripePaymentIntent = {
            id: paymentIntent.id,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            status: paymentIntent.status,
            created: paymentIntent.created,
            description: paymentIntent.description,
            receipt_email: paymentIntent.receipt_email,
            payment_method_types: paymentIntent.payment_method_types,
          };

          // Get payment method details if available
          if (paymentIntent.payment_method && typeof paymentIntent.payment_method === 'object') {
            const paymentMethod = paymentIntent.payment_method as any;
            paymentDetails.paymentMethod = {
              type: paymentMethod.type,
              card: paymentMethod.card ? {
                brand: paymentMethod.card.brand,
                last4: paymentMethod.card.last4,
                exp_month: paymentMethod.card.exp_month,
                exp_year: paymentMethod.card.exp_year,
                funding: paymentMethod.card.funding,
              } : null,
            };
          }

          // Get charges information separately
          try {
            const charges = await stripe.charges.list({
              payment_intent: paymentIntent.id,
              limit: 1
            });

            if (charges.data.length > 0) {
              const charge = charges.data[0];
              paymentDetails.charge = {
                id: charge.id,
                amount: charge.amount,
                currency: charge.currency,
                status: charge.status,
                created: charge.created,
                paid: charge.paid,
                refunded: charge.refunded,
                amount_refunded: charge.amount_refunded,
                receipt_url: charge.receipt_url,
                billing_details: charge.billing_details,
                outcome: charge.outcome,
              };
            }
          } catch (chargeError) {
            console.error('Error fetching charges:', chargeError);
          }
        } catch (stripeError) {
          console.error('Error fetching Stripe payment intent:', stripeError);
          paymentDetails.stripeError = 'Unable to fetch payment details from Stripe';
        }
      }

      // Fetch Stripe Session details if available
      if (orderData?.stripeSessionId) {
        try {
          const session = await stripe.checkout.sessions.retrieve(orderData.stripeSessionId);
          paymentDetails.stripeSession = {
            id: session.id,
            payment_status: session.payment_status,
            status: session.status,
            amount_total: session.amount_total,
            currency: session.currency,
            created: session.created,
            expires_at: session.expires_at,
            customer_email: session.customer_details?.email,
            customer_name: session.customer_details?.name,
            payment_method_types: session.payment_method_types,
          };
        } catch (stripeError) {
          console.error('Error fetching Stripe session:', stripeError);
          paymentDetails.sessionError = 'Unable to fetch session details from Stripe';
        }
      }

      return paymentDetails;
    } catch (error) {
      console.error('Error in getPaymentDetails:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Create Stripe Connect account
export const createStripeConnectAccount = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { accountType } = data;
      const userId = context.auth.uid;

      if (!accountType || !['student', 'merchant'].includes(accountType)) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid accountType is required');
      }

      console.log(`Creating Stripe Connect account for user ${userId}, type: ${accountType}`);

      // Check if user already has a connect account
      const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
      
      if (existingAccountDoc.exists) {
        const existingAccount = existingAccountDoc.data();
        console.log('User already has a connect account:', existingAccount?.stripeAccountId);
        
        return {
          accountId: existingAccount?.stripeAccountId,
          onboardingUrl: existingAccount?.onboardingUrl || null,
          message: 'Account already exists'
        };
      }

      // Get user data
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User profile not found');
      }

      const userData = userDoc.data();

      // Create a real Stripe Connect Express account
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: userData?.email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        business_type: accountType === 'merchant' ? 'company' : 'individual',
        metadata: {
          userId,
          accountType,
          platform: 'hive_campus'
        }
      });

      console.log(`Created Stripe Connect account ${account.id} for user ${userId}`);

      // Create account link for onboarding
      const accountLink = await stripe.accountLinks.create({
        account: account.id,
        refresh_url: `${process.env.APP_URL || 'https://h1c1-798a8.web.app'}/settings/payment?refresh=true`,
        return_url: `${process.env.APP_URL || 'https://h1c1-798a8.web.app'}/settings/payment?success=true`,
        type: 'account_onboarding',
      });

      // Store the Connect account in Firestore
      await admin.firestore().collection('connectAccounts').doc(userId).set({
        userId: userId,
        stripeAccountId: account.id,
        accountType: accountType,
        isOnboarded: false,
        chargesEnabled: account.charges_enabled || false,
        payoutsEnabled: account.payouts_enabled || false,
        detailsSubmitted: account.details_submitted || false,
        onboardingUrl: accountLink.url,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      });

      console.log(`Stored Connect account ${account.id} in Firestore for user ${userId}`);

      return {
        accountId: account.id,
        onboardingUrl: accountLink.url,
        message: 'Connect account created successfully'
      };

    } catch (error) {
      console.error('Error in createStripeConnectAccount:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Test function to verify deployment
export const testStripeConnectReal = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Real Stripe Connect functions deployed successfully',
      timestamp: new Date().toISOString(),
      functions: [
        'getStripeConnectAccountStatus',
        'getSellerPendingPayouts', 
        'createStripeConnectAccount'
      ]
    });
  });
