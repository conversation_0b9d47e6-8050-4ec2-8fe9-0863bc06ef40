<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear PIN Session</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #333;
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #c82333;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Clear PIN Session</h1>
        
        <div class="info">
            <strong>Current Session Status:</strong><br>
            <span id="sessionStatus">Checking...</span>
        </div>
        
        <button onclick="clearPinSession()">Clear PIN Session</button>
        <button onclick="checkSession()">Check Session</button>
        
        <div id="result" style="display: none;"></div>
        
        <div class="info">
            <strong>Instructions:</strong><br>
            1. Click "Clear PIN Session" to remove any stored PIN verification<br>
            2. Go to the admin dashboard - you should now be prompted for PIN<br>
            3. This forces the PIN authentication to show up
        </div>
    </div>

    <script>
        function checkSession() {
            const pinVerified = sessionStorage.getItem('adminPinVerified');
            const pinTime = sessionStorage.getItem('adminPinTime');
            
            let status = '';
            if (pinVerified === 'true' && pinTime) {
                const timeDiff = Date.now() - parseInt(pinTime);
                const minutesAgo = Math.floor(timeDiff / (1000 * 60));
                status = `✅ PIN verified ${minutesAgo} minutes ago`;
            } else {
                status = '❌ No PIN verification found';
            }
            
            document.getElementById('sessionStatus').innerHTML = status;
        }
        
        function clearPinSession() {
            sessionStorage.removeItem('adminPinVerified');
            sessionStorage.removeItem('adminPinTime');
            
            const result = document.getElementById('result');
            result.className = 'success';
            result.innerHTML = '✅ PIN session cleared successfully!<br>Now go to the admin dashboard - you should be prompted for PIN.';
            result.style.display = 'block';
            
            checkSession();
        }
        
        // Check session on load
        checkSession();
    </script>
</body>
</html>
