"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.refundTransaction = void 0;
// Simple refund function to fix CORS issues
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const stripe_1 = __importDefault(require("stripe"));
const cors_1 = __importDefault(require("cors"));
// Initialize Stripe
const stripe = new stripe_1.default(((_a = functions.config().stripe) === null || _a === void 0 ? void 0 : _a.api_key) || process.env.STRIPE_API_KEY || '', {
    apiVersion: '2025-05-28.basil',
});
// CORS configuration for development and production
const corsHandler = (0, cors_1.default)({
    origin: [
        'https://h1c1-798a8.web.app',
        'https://h1c1-798a8.firebaseapp.com',
        'https://hivecampus.app',
        'https://www.hivecampus.app',
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:3000',
        'http://localhost:5000'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});
// Refund transaction function
exports.refundTransaction = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    var _a, _b;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const { orderId, amount, reason } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get order details
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Check if order can be refunded
        if (!(orderData === null || orderData === void 0 ? void 0 : orderData.stripePaymentIntentId) && !(orderData === null || orderData === void 0 ? void 0 : orderData.stripeSessionId)) {
            throw new functions.https.HttpsError('failed-precondition', 'No payment information found for refund');
        }
        // Process refund with Stripe
        let refund;
        try {
            if (orderData.stripePaymentIntentId) {
                refund = await stripe.refunds.create({
                    payment_intent: orderData.stripePaymentIntentId,
                    amount: amount ? Math.round(amount * 100) : undefined, // Convert to cents
                    reason: 'requested_by_customer',
                    metadata: {
                        orderId,
                        adminId: context.auth.uid,
                        reason: reason || 'Admin refund'
                    }
                });
            }
            else {
                throw new functions.https.HttpsError('failed-precondition', 'Cannot process refund - invalid payment method');
            }
        }
        catch (stripeError) {
            console.error('Stripe refund error:', stripeError);
            throw new functions.https.HttpsError('internal', `Refund failed: ${stripeError.message}`);
        }
        // Update order status
        await orderRef.update({
            status: 'refunded',
            refundId: refund.id,
            refundAmount: refund.amount / 100,
            refundReason: reason || 'Admin refund',
            refundedAt: admin.firestore.Timestamp.now(),
            refundedBy: context.auth.uid,
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Update listing back to active if needed
        if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
            const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
            const listingDoc = await listingRef.get();
            if (listingDoc.exists && ((_b = listingDoc.data()) === null || _b === void 0 ? void 0 : _b.status) === 'sold') {
                await listingRef.update({
                    status: 'active',
                    updatedAt: admin.firestore.Timestamp.now()
                });
            }
        }
        // Send notification to buyer
        if (orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) {
            try {
                await admin.firestore().collection(`users/${orderData.buyerId}/notifications`).add({
                    type: 'refund_processed',
                    title: 'Refund Processed',
                    message: `Your refund of $${(refund.amount / 100).toFixed(2)} has been processed and will appear in your account within 5-10 business days.`,
                    icon: '/icons/icon-192.png',
                    createdAt: admin.firestore.Timestamp.now(),
                    read: false,
                    link: `/orders/${orderId}`,
                    priority: 'high',
                    actionRequired: false,
                    metadata: {
                        refundAmount: refund.amount / 100,
                        refundId: refund.id
                    },
                    orderId: orderId
                });
            }
            catch (notificationError) {
                console.error('Error sending refund notification:', notificationError);
            }
        }
        console.log(`✅ Refund processed: ${refund.id} for order: ${orderId}`);
        return {
            success: true,
            message: 'Refund processed successfully',
            refundId: refund.id,
            refundAmount: refund.amount / 100
        };
    }
    catch (error) {
        console.error('Error processing refund:', error);
        throw error;
    }
});
