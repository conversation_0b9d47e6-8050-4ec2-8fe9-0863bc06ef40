import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Star, MapPin, Calendar, Edit, Grid, List, Camera, Save, X, GraduationCap, BookOpen, Package, Trash2 } from 'lucide-react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useListings } from '../hooks/useListings';
import { useStripeConnect } from '../hooks/useStripeConnect';
import { updateUserProfile } from '../firebase/auth';
import { uploadFile } from '../firebase/uploads';
import { formatTimestamp } from '../utils/timestamp';

import { collection, getDocs, query, where } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { Listing } from '../firebase/types';
import OnboardingStatusBadge from '../components/OnboardingStatusBadge';
import StripeOnboardingModal from '../components/StripeOnboardingModal';

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { currentUser, userProfile, userRole, refreshProfile } = useAuth();
  const { fetchListings, listings, isLoading: listingsLoading, removeListing } = useListings();
  const { accountStatus, totalPendingAmount, pendingOrderCount } = useStripeConnect({ autoLoad: true });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isEditing, setIsEditing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showOnboardingModal, setShowOnboardingModal] = useState(false);
  const [deletingListingId, setDeletingListingId] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [listingToDelete, setListingToDelete] = useState<string | null>(null);

  // Fallback state for direct Firestore access
  const [fallbackListings, setFallbackListings] = useState<Listing[]>([]);
  const [isFallbackLoading, setIsFallbackLoading] = useState(false);
  const [useFallback, setUseFallback] = useState(false);

  // Form state for editing
  const [editForm, setEditForm] = useState({
    name: '',
    bio: '',
    graduationYear: '',
    major: ''
  });

  // Initialize form with user data
  useEffect(() => {
    if (userProfile) {
      setEditForm({
        name: userProfile.name || '',
        bio: userProfile.bio || '',
        graduationYear: userProfile.graduationYear?.toString() || '',
        major: userProfile.major || ''
      });
    }
  }, [userProfile]);

  // Refresh profile data if missing (for new users, but not for admin users)
  useEffect(() => {
    if (currentUser && !userProfile && userRole !== 'admin') {
      console.log('Profile data missing, attempting to refresh...');
      refreshProfile();
    }
  }, [currentUser, userProfile, userRole, refreshProfile]);

  // Stripe Connect data is auto-loaded via useStripeConnect({ autoLoad: true })

  // Optimized fallback function with memoization
  const fetchUserListingsDirectly = useCallback(async (userId: string) => {
    try {
      setIsFallbackLoading(true);

      const listingsRef = collection(firestore, 'listings');
      const q = query(listingsRef, where('ownerId', '==', userId));

      const snapshot = await getDocs(q);
      const directListings: Listing[] = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Listing));

      setFallbackListings(directListings);
      setUseFallback(true);
    } catch (error) {
      console.error('Error fetching user listings directly:', error);
    } finally {
      setIsFallbackLoading(false);
    }
  }, []);

  // Optimized listings fetch with error handling
  useEffect(() => {
    if (!currentUser?.uid) return;

    const loadUserListings = async () => {
      try {
        const result = await fetchListings({ ownerId: currentUser.uid });

        // If hook fetch fails or returns no data, use direct fetch
        if (!result || !(result as any).success || !(result as any).data?.listings?.length) {
          await fetchUserListingsDirectly(currentUser.uid);
        }
      } catch (error) {
        console.error('Error fetching user listings:', error);
        await fetchUserListingsDirectly(currentUser.uid);
      }
    };

    loadUserListings();
  }, [currentUser?.uid, fetchListings, fetchUserListingsDirectly]);



  // Memoized computed values for better performance
  const userListings = useMemo(() =>
    useFallback ? fallbackListings : listings,
    [useFallback, fallbackListings, listings]
  );

  const visibleListings = useMemo(() =>
    userListings.filter(l => l.status !== 'deleted'),
    [userListings]
  );

  const userStats = useMemo(() => {
    const activeListings = visibleListings.filter(l => l.status === 'active').length;
    const soldListings = visibleListings.filter(l => l.status === 'sold').length;
    const totalListings = visibleListings.length;
    const totalRevenue = visibleListings
      .filter(l => l.status === 'sold')
      .reduce((sum, l) => sum + (l.price || 0), 0);

    return {
      totalSales: soldListings,
      totalPurchases: 0, // TODO: Track purchases when order system is implemented
      totalListings,
      activeListings,
      totalRevenue,
      responseRate: 0, // Will be calculated from real message response data when implemented
      averagePrice: totalListings > 0 ? visibleListings.reduce((sum, l) => sum + (l.price || 0), 0) / totalListings : 0
    };
  }, [visibleListings]);

  const user = useMemo(() => ({
    name: userProfile?.name || 'User',
    username: `@${userProfile?.email?.split('@')[0] || 'user'}`,
    avatar: userProfile?.profilePictureURL || '/placeholder-avatar.svg',
    rating: 0, // Will be calculated from real reviews when implemented
    reviewCount: 0, // Will count real reviews when implemented
    joinedDate: formatTimestamp(userProfile?.createdAt, { month: 'long', year: 'numeric' }),
    location: userProfile?.university || 'University',
    bio: userProfile?.bio || 'No bio available',
    graduationYear: userProfile?.graduationYear,
    major: userProfile?.major,
    stats: userStats
  }), [userProfile, userStats]);

  // Optimized handler functions with useCallback
  const handleImageUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const result = await uploadFile(file, 'profile');
      await updateUserProfile({ profilePictureURL: result.downloadURL });
      window.location.reload();
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, []);

  const handleSaveProfile = useCallback(async () => {
    if (!editForm.name.trim()) {
      alert('Name is required');
      return;
    }

    setIsSaving(true);
    try {
      // Try using the new Firebase Function first (if available)
      try {
        const { httpsCallable } = await import('firebase/functions');
        const { functions } = await import('../firebase/config');

        const updateProfile = httpsCallable(functions, 'updateProfile');
        const result = await updateProfile({
          name: editForm.name.trim(),
          bio: editForm.bio.trim(),
          major: editForm.major.trim(),
          graduationYear: editForm.graduationYear ? parseInt(editForm.graduationYear) : undefined
        });

        if (result.data && (result.data as any).success) {
          setIsEditing(false);
          await refreshProfile(); // Refresh the profile data
          alert('Profile updated successfully!');
          return;
        }
      } catch (functionError) {
        console.warn('Firebase Function not available or failed, falling back to direct update:', functionError);
      }

      // Fallback to direct Firestore update
      const updateData: any = {
        name: editForm.name.trim(),
        bio: editForm.bio.trim(),
        major: editForm.major.trim()
      };

      if (editForm.graduationYear) {
        const year = parseInt(editForm.graduationYear);
        if (year >= 2020 && year <= 2035) {
          updateData.graduationYear = year;
        }
      }

      await updateUserProfile(updateData);
      setIsEditing(false);
      await refreshProfile(); // Refresh the profile data
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please check your internet connection and try again.');
    } finally {
      setIsSaving(false);
    }
  }, [editForm, refreshProfile]);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
    if (userProfile) {
      setEditForm({
        name: userProfile.name || '',
        bio: userProfile.bio || '',
        graduationYear: userProfile.graduationYear?.toString() || '',
        major: userProfile.major || ''
      });
    }
  }, [userProfile]);

  const handleDeleteListing = (listingId: string) => {
    setListingToDelete(listingId);
    setShowDeleteModal(true);
  };

  const confirmDeleteListing = async () => {
    if (!listingToDelete) return;

    setDeletingListingId(listingToDelete);
    setShowDeleteModal(false);

    try {
      await removeListing(listingToDelete);
      // Refresh the listings
      if (currentUser) {
        fetchUserListingsDirectly(currentUser.uid);
      }
    } catch (error) {
      console.error('Error deleting listing:', error);
      alert('Failed to delete listing. Please try again.');
    } finally {
      setDeletingListingId(null);
      setListingToDelete(null);
    }
  };

  const cancelDeleteListing = () => {
    setShowDeleteModal(false);
    setListingToDelete(null);
  };

  const handleEditListing = (listingId: string) => {
    navigate(`/edit-listing/${listingId}`);
  };

  // Use real listings data from the hook (listings is already available from the hook)

  // TODO: Replace with real reviews data when review system is implemented
  const reviews: any[] = [];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-6xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">
        {/* Profile Header */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="relative">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-32 h-32 rounded-2xl object-cover"
              />
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                id="avatar-upload"
              />
              <label
                htmlFor="avatar-upload"
                className="absolute -bottom-2 -right-2 bg-primary-500 text-white p-2 rounded-full hover:bg-primary-600 transition-colors cursor-pointer"
              >
                {isUploading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Camera className="w-4 h-4" />
                )}
              </label>
            </div>

            {/* User Info */}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-4">
                {isEditing ? (
                  <div className="flex-1 mr-4">
                    <input
                      type="text"
                      value={editForm.name}
                      onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                      className="text-3xl font-bold bg-transparent border-b-2 border-primary-500 text-gray-900 dark:text-white focus:outline-none w-full"
                      placeholder="Your name"
                    />
                  </div>
                ) : (
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{user.name}</h1>
                )}
                <div className="flex items-center space-x-1">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  <span className="font-semibold text-gray-900 dark:text-white">{user.rating}</span>
                  <span className="text-gray-500 dark:text-gray-400">({user.reviewCount} reviews)</span>
                </div>
              </div>

              <p className="text-gray-600 dark:text-gray-400 mb-4">{user.username}</p>

              <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mb-4">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span>{user.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>Joined {user.joinedDate}</span>
                </div>
                {user.graduationYear && (
                  <div className="flex items-center space-x-2">
                    <GraduationCap className="w-4 h-4" />
                    <span>Class of {user.graduationYear}</span>
                  </div>
                )}
                {user.major && (
                  <div className="flex items-center space-x-2">
                    <BookOpen className="w-4 h-4" />
                    <span>{user.major}</span>
                  </div>
                )}
              </div>

              {isEditing ? (
                <div className="space-y-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bio</label>
                    <textarea
                      value={editForm.bio}
                      onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      rows={3}
                      placeholder="Tell us about yourself..."
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Major</label>
                      <input
                        type="text"
                        value={editForm.major}
                        onChange={(e) => setEditForm({ ...editForm, major: e.target.value })}
                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder="Your major"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Graduation Year</label>
                      <input
                        type="number"
                        value={editForm.graduationYear}
                        onChange={(e) => setEditForm({ ...editForm, graduationYear: e.target.value })}
                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder="2025"
                        min="2020"
                        max="2030"
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-700 dark:text-gray-300 mb-6">{user.bio}</p>
              )}

              {/* Onboarding Status Badge */}
              {userProfile && (
                <div className="mb-6">
                  <OnboardingStatusBadge
                    userId={userProfile.uid}
                    isOnboarded={accountStatus?.isOnboarded || false}
                    pendingAmount={totalPendingAmount}
                    orderCount={pendingOrderCount}
                    onSetupClick={() => setShowOnboardingModal(true)}
                    variant="detailed"
                  />
                </div>
              )}

              {/* Edit/Save buttons */}
              <div className="flex items-center space-x-3 mb-6">
                {isEditing ? (
                  <>
                    <button
                      onClick={handleSaveProfile}
                      disabled={isSaving}
                      className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                    >
                      {isSaving ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <Save className="w-4 h-4" />
                      )}
                      <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <X className="w-4 h-4" />
                      <span>Cancel</span>
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                    <span>Edit Profile</span>
                  </button>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">{user.stats.totalSales}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Sales</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-success-600 dark:text-success-400">{user.stats.totalPurchases}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Purchases</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-accent-600 dark:text-accent-400">{user.stats.totalListings}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Listings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{user.stats.responseRate}%</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Response Rate</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Listings */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">My Listings</h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg ${
                      viewMode === 'grid'
                        ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                        : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                  >
                    <Grid className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg ${
                      viewMode === 'list'
                        ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                        : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                  >
                    <List className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {(listingsLoading || isFallbackLoading) ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
                </div>
              ) : visibleListings.length === 0 ? (
                <div className="text-center py-12">
                  <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No listings yet</h4>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">Start selling by creating your first listing</p>
                  <Link
                    to="/add-listing"
                    className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    Create Listing
                  </Link>
                </div>
              ) : (
                viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {visibleListings.map((listing) => (
                      <div key={listing.id} className="group relative">
                        <Link to={`/listing/${listing.id}`}>
                          <div className="cursor-pointer">
                            <div className="relative aspect-square rounded-xl overflow-hidden mb-3">
                              <img
                                src={listing.imageURLs?.[0] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400'}
                                alt={listing.title}
                                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                              />
                              <div className="absolute top-3 left-3">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  listing.status === 'active'
                                    ? 'bg-success-100 text-success-700'
                                    : listing.status === 'sold'
                                    ? 'bg-orange-100 text-orange-700'
                                    : 'bg-gray-100 text-gray-700'
                                }`}>
                                  {listing.status === 'sold' ? 'SOLD' : listing.status.toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">{listing.title}</h3>
                            <p className="text-xl font-bold text-primary-600 dark:text-primary-400 mb-2">${listing.price}</p>
                            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                              <span>{listing.category}</span>
                              <span>{listing.condition}</span>
                            </div>
                          </div>
                        </Link>

                        {/* Edit and Delete buttons */}
                        <div className="absolute top-3 right-3 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleEditListing(listing.id!);
                            }}
                            className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            title="Edit listing"
                          >
                            <Edit className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDeleteListing(listing.id!);
                            }}
                            disabled={deletingListingId === listing.id}
                            className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-red-50 dark:hover:bg-red-900 transition-colors disabled:opacity-50"
                            title="Delete listing"
                          >
                            {deletingListingId === listing.id ? (
                              <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
                            ) : (
                              <Trash2 className="w-4 h-4 text-red-500" />
                            )}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {visibleListings.map((listing) => (
                      <div key={listing.id} className="group relative">
                        <Link to={`/listing/${listing.id}`}>
                          <div className="flex items-center space-x-4 p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <img
                              src={listing.imageURLs?.[0] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400'}
                              alt={listing.title}
                              className="w-16 h-16 rounded-lg object-cover"
                            />
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 dark:text-white">{listing.title}</h3>
                              <p className="text-lg font-bold text-primary-600 dark:text-primary-400">${listing.price}</p>
                            </div>
                            <div className="text-right">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                listing.status === 'active'
                                  ? 'bg-success-100 text-success-700'
                                  : listing.status === 'sold'
                                  ? 'bg-orange-100 text-orange-700'
                                  : 'bg-gray-100 text-gray-700'
                              }`}>
                                {listing.status === 'sold' ? 'SOLD' : listing.status.toUpperCase()}
                              </span>
                              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {listing.category} • {listing.condition}
                              </div>
                            </div>
                          </div>
                        </Link>

                        {/* Edit and Delete buttons */}
                        <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleEditListing(listing.id!);
                            }}
                            className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            title="Edit listing"
                          >
                            <Edit className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDeleteListing(listing.id!);
                            }}
                            disabled={deletingListingId === listing.id}
                            className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-red-50 dark:hover:bg-red-900 transition-colors disabled:opacity-50"
                            title="Delete listing"
                          >
                            {deletingListingId === listing.id ? (
                              <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
                            ) : (
                              <Trash2 className="w-4 h-4 text-red-500" />
                            )}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )
              )}
            </div>
          </div>

          {/* Reviews */}
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Recent Reviews</h3>
              <div className="space-y-6">
                {reviews.map((review) => (
                  <div key={review.id} className="border-b border-gray-200 dark:border-gray-700 last:border-0 pb-6 last:pb-0">
                    <div className="flex items-start space-x-3">
                      <img
                        src={review.avatar}
                        alt={review.reviewer}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-semibold text-gray-900 dark:text-white text-sm">{review.reviewer}</h4>
                          <div className="flex">
                            {[...Array(review.rating)].map((_, i) => (
                              <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                            ))}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{review.comment}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-500">{review.date}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stripe Onboarding Modal */}
      <StripeOnboardingModal
        isOpen={showOnboardingModal}
        onClose={() => setShowOnboardingModal(false)}
        pendingAmount={totalPendingAmount}
        orderCount={pendingOrderCount}
        onOnboardingComplete={() => {
          setShowOnboardingModal(false);
          // Optionally refresh account status or show success message
        }}
      />

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mr-4">
                <Trash2 className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Delete Listing
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  This action cannot be undone
                </p>
              </div>
            </div>

            <p className="text-gray-700 dark:text-gray-300 mb-6">
              Are you sure you want to permanently delete this listing? This will remove it from your profile and it cannot be recovered.
            </p>

            <div className="flex space-x-3">
              <button
                onClick={cancelDeleteListing}
                className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Don't Delete
              </button>
              <button
                onClick={confirmDeleteListing}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Profile;