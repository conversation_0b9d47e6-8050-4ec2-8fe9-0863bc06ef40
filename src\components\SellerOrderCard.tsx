import React, { useState } from 'react';
import { 
  Clock, Package, Truck, CheckCircle, AlertCircle, Eye, RotateCcw, 
  MessageCircle, Copy, ExternalLink, Shield, MapPin, Calendar,
  DollarSign, User, FileText, Download, Send
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { Order } from '../firebase/types';
import { formatPrice } from '../utils/priceUtils';

interface SellerOrderCardProps {
  order: Order;
  onOrderUpdate?: () => void;
  isSelected?: boolean;
  onSelect?: () => void;
}

const SellerOrderCard: React.FC<SellerOrderCardProps> = ({ order, onOrderUpdate, isSelected, onSelect }) => {
  const { markDeliveryCompleted, generateShippingLabel, isLoading } = useStripeCheckout();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [generatingLabel, setGeneratingLabel] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'delivered':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'return_requested':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'In Progress';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'completed':
        return 'Completed';
      case 'return_requested':
        return 'Return Requested';
      default:
        return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in_progress':
        return <Clock className="w-4 h-4" />;
      case 'shipped':
        return <Truck className="w-4 h-4" />;
      case 'delivered':
        return <Package className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'return_requested':
        return <RotateCcw className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const handleMarkDelivered = async () => {
    try {
      setError(null);
      await markDeliveryCompleted(order.id);
      setSuccess('Order marked as delivered! Buyer can now enter the secret code.');
      onOrderUpdate?.();
      
      // Log to memory
      console.log(`📦 SELLER ACTION: Marked order ${order.id} as delivered`);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to mark as delivered');
    }
  };

  const handleGenerateShippingLabel = async () => {
    try {
      setGeneratingLabel(true);
      setError(null);
      const result = await generateShippingLabel(order.id);
      setSuccess('Shipping label generated successfully!');
      onOrderUpdate?.();
      
      // Log to memory
      console.log(`🏷️ SELLER ACTION: Generated shipping label for order ${order.id}`);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate shipping label');
    } finally {
      setGeneratingLabel(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setSuccess('Copied to clipboard!');
    setTimeout(() => setSuccess(null), 2000);
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 border transition-all cursor-pointer ${
        isSelected
          ? 'border-primary-500 ring-2 ring-primary-200 dark:ring-primary-800'
          : 'border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600'
      }`}
      onClick={() => {
        // Navigate to dedicated order management page
        window.location.href = `/orders/my-sales/${order.id}`;
      }}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start space-x-4">
          <img
            src={order.listingImage || '/placeholder-image.jpg'}
            alt={order.title || order.listingTitle}
            className="w-16 h-16 rounded-xl object-cover"
          />
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
              {order.title || order.listingTitle}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Order #{order.id.slice(-8)} • Seller View
            </p>
            <p className="text-lg font-bold text-green-600 dark:text-green-400">
              You receive: ${formatPrice(order.sellerAmount || order.amount)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Total order: ${formatPrice(order.finalStripeAmount || order.amount)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {getStatusIcon(order.status)}
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
            {getStatusText(order.status)}
          </span>
        </div>
      </div>

      {/* Delivery Type & Instructions */}
      <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Delivery: {order.deliveryType === 'shipping' ? 'Shipping' : 'In-Person'}
            </span>
          </div>
        </div>

        {order.deliveryType === 'shipping' ? (
          <div className="space-y-2">
            {order.shippingLabelUrl ? (
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-600">Shipping label ready</span>
                <a
                  href={order.shippingLabelUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-700 text-sm"
                >
                  <Download className="w-4 h-4 inline" />
                </a>
              </div>
            ) : (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Generate shipping label to get started
              </p>
            )}
            
            {order.shippingTrackingNumber && (
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-600 dark:text-gray-400">Tracking:</span>
                <code className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                  {order.shippingTrackingNumber}
                </code>
                <button
                  onClick={() => copyToClipboard(order.shippingTrackingNumber!)}
                  className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <Copy className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Meet with buyer in person to complete delivery
          </p>
        )}
      </div>

      {/* Delivery Instructions for Seller */}
      {(order.status === 'payment_succeeded' || order.status === 'payment_completed') && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-green-600 dark:text-green-400" />
            <div>
              <p className="text-sm font-medium text-green-900 dark:text-green-200">Ready for Delivery</p>
              <p className="text-xs text-green-700 dark:text-green-300">
                Complete the delivery and the buyer will receive a secret code to release payment
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Error/Success Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <p className="text-sm text-green-600 dark:text-green-400">{success}</p>
        </div>
      )}

      {/* Enhanced Action Panel - Only show when selected */}
      {isSelected && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sales Actions</h4>

          {/* Delivery Type Specific Actions */}
          {order.deliveryType === 'shipping' ? (
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-4 mb-4">
              <h5 className="font-medium text-purple-900 dark:text-purple-200 mb-3">📦 Shipping Management</h5>
              <div className="space-y-3">
                {!order.shippingLabelUrl && order.status === 'in_progress' && (
                  <button
                    onClick={handleGenerateShippingLabel}
                    disabled={generatingLabel}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                  >
                    <Send className="w-5 h-5" />
                    <span>{generatingLabel ? 'Generating Shippo Label...' : 'Generate Shipping Label'}</span>
                  </button>
                )}

                {order.shippingLabelUrl && (
                  <a
                    href={order.shippingLabelUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Download className="w-5 h-5" />
                    <span>Download Shipping Label</span>
                  </a>
                )}

                {order.shippingTrackingNumber && (
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Tracking Number:</p>
                    <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                      {order.shippingTrackingNumber}
                    </code>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-4 mb-4">
              <h5 className="font-medium text-green-900 dark:text-green-200 mb-3">🤝 In-Person Delivery</h5>
              {order.status === 'in_progress' && (
                <button
                  onClick={handleMarkDelivered}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  <Package className="w-5 h-5" />
                  <span>Confirm In-Person Delivery</span>
                </button>
              )}
            </div>
          )}

          {/* Action Buttons Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {/* Track Order */}
            <Link
              to={`/order/${order.id}`}
              className="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
            >
              <Truck className="w-6 h-6 text-blue-600 dark:text-blue-400 mb-2" />
              <span className="text-sm font-medium text-blue-900 dark:text-blue-200">Track Order</span>
            </Link>

            {/* Chat with Buyer */}
            <Link
              to="/messages"
              state={{
                buyerId: order.buyerId,
                orderId: order.id,
                listingId: order.listingId
              }}
              className="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-xl hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
            >
              <MessageCircle className="w-6 h-6 text-green-600 dark:text-green-400 mb-2" />
              <span className="text-sm font-medium text-green-900 dark:text-green-200">Chat with Buyer</span>
            </Link>

            {/* View Item */}
            <Link
              to={`/listing/${order.listingId}`}
              className="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            >
              <Eye className="w-6 h-6 text-gray-600 dark:text-gray-400 mb-2" />
              <span className="text-sm font-medium text-gray-900 dark:text-gray-200">View Item</span>
            </Link>
          </div>
        </div>
      )}

      {/* Compact Action Buttons - Only show when not selected */}
      {!isSelected && (
        <div className="flex flex-wrap gap-2 mb-4">
          {/* Shipping Actions */}
          {order.deliveryType === 'shipping' && (
            <>
              {!order.shippingLabelUrl && order.status === 'in_progress' && (
                <button
                  onClick={handleGenerateShippingLabel}
                  disabled={generatingLabel}
                  className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  <Send className="w-4 h-4" />
                  <span>{generatingLabel ? 'Generating...' : 'Generate Label'}</span>
                </button>
              )}

              {order.shippingLabelUrl && (
                <a
                  href={order.shippingLabelUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Download className="w-4 h-4" />
                  <span>Download Label</span>
                </a>
              )}
            </>
          )}

          {/* In-Person Delivery */}
          {order.deliveryType === 'in_person' && order.status === 'in_progress' && (
            <button
              onClick={handleMarkDelivered}
              disabled={isLoading}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              <Package className="w-4 h-4" />
              <span>Mark as Delivered</span>
            </button>
          )}

          {/* Message Buyer */}
          <Link
            to="/messages"
            state={{
              buyerId: order.buyerId,
              orderId: order.id,
              listingId: order.listingId
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <MessageCircle className="w-4 h-4" />
            <span>Message Buyer</span>
          </Link>

          {/* Toggle Details */}
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <FileText className="w-4 h-4" />
            <span>{showDetails ? 'Hide' : 'Show'} Details</span>
          </button>
        </div>
      )}

      {/* Expanded Details */}
      {showDetails && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Order Date:</span>
              <p className="font-medium">{new Date(order.createdAt.toDate()).toLocaleDateString()}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Platform Fee:</span>
              <p className="font-medium">${formatPrice(order.platformFee || 0)}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Buyer Paid:</span>
              <p className="font-medium">${formatPrice(order.finalStripeAmount || order.amount)}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Funds Released:</span>
              <p className="font-medium">{order.releasedToSeller ? 'Yes' : 'No'}</p>
            </div>
          </div>

          {/* Buyer Contact Info */}
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2">Buyer Information</h4>
            <div className="space-y-1 text-sm">
              <p className="text-blue-800 dark:text-blue-300">
                <User className="w-4 h-4 inline mr-1" />
                Buyer ID: {order.buyerId.slice(-8)}
              </p>
              {order.shippingAddress && (
                <div className="text-blue-800 dark:text-blue-300">
                  <MapPin className="w-4 h-4 inline mr-1" />
                  <span>
                    {order.shippingAddress.name}, {order.shippingAddress.city}, {order.shippingAddress.state}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SellerOrderCard;
