#!/usr/bin/env node

/**
 * 🔐 COMPREHENSIVE SECURITY AUDIT SCRIPT
 * Hive Campus Production Security Validation
 * 
 * Runs complete security validation suite and generates detailed report
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const AUDIT_TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-');
const REPORT_FILE = `security-audit-${AUDIT_TIMESTAMP}.json`;

class SecurityAuditor {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      overallStatus: 'PENDING',
      securityScore: 0,
      testResults: {},
      vulnerabilities: [],
      recommendations: [],
      productionReadiness: false
    };
  }

  async runComprehensiveAudit() {
    console.log('🔐 Starting Comprehensive Security Audit for Hive Campus...\n');

    try {
      // 1. Run Security Validation Tests
      await this.runSecurityTests();
      
      // 2. Run Backend Logic Tests
      await this.runBackendLogicTests();
      
      // 3. Run Frontend Security Tests
      await this.runFrontendSecurityTests();
      
      // 4. Run Production Readiness Tests
      await this.runProductionReadinessTests();
      
      // 5. Validate Firestore Rules
      await this.validateFirestoreRules();
      
      // 6. Check Dependencies for Vulnerabilities
      await this.checkDependencyVulnerabilities();
      
      // 7. Validate Environment Configuration
      await this.validateEnvironmentConfig();
      
      // 8. Generate Final Report
      await this.generateFinalReport();
      
    } catch (error) {
      console.error('❌ Audit failed:', error);
      this.results.overallStatus = 'FAILED';
      this.results.vulnerabilities.push({
        severity: 'CRITICAL',
        category: 'AUDIT_FAILURE',
        description: error.message
      });
    }
  }

  async runSecurityTests() {
    console.log('🛡️ Running Security Validation Tests...');
    
    try {
      const output = execSync('npm run test tests/security-validation.test.js', { 
        encoding: 'utf8',
        timeout: 60000 
      });
      
      this.results.testResults.securityValidation = {
        status: 'PASSED',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Security validation tests passed');
    } catch (error) {
      this.results.testResults.securityValidation = {
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      
      this.results.vulnerabilities.push({
        severity: 'HIGH',
        category: 'SECURITY_TEST_FAILURE',
        description: 'Security validation tests failed',
        details: error.message
      });
      
      console.log('❌ Security validation tests failed');
    }
  }

  async runBackendLogicTests() {
    console.log('⚙️ Running Backend Logic Flow Tests...');
    
    try {
      const output = execSync('npm run test tests/backend-logic-flow.test.js', { 
        encoding: 'utf8',
        timeout: 60000 
      });
      
      this.results.testResults.backendLogic = {
        status: 'PASSED',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Backend logic tests passed');
    } catch (error) {
      this.results.testResults.backendLogic = {
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      
      this.results.vulnerabilities.push({
        severity: 'HIGH',
        category: 'LOGIC_VULNERABILITY',
        description: 'Backend logic validation failed',
        details: error.message
      });
      
      console.log('❌ Backend logic tests failed');
    }
  }

  async runFrontendSecurityTests() {
    console.log('📲 Running Frontend Security Tests...');
    
    try {
      const output = execSync('npm run test tests/frontend-security.test.js', { 
        encoding: 'utf8',
        timeout: 60000 
      });
      
      this.results.testResults.frontendSecurity = {
        status: 'PASSED',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Frontend security tests passed');
    } catch (error) {
      this.results.testResults.frontendSecurity = {
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      
      this.results.vulnerabilities.push({
        severity: 'MEDIUM',
        category: 'FRONTEND_VULNERABILITY',
        description: 'Frontend security validation failed',
        details: error.message
      });
      
      console.log('❌ Frontend security tests failed');
    }
  }

  async runProductionReadinessTests() {
    console.log('🚀 Running Production Readiness Tests...');
    
    try {
      const output = execSync('npm run test tests/production-readiness.test.js', { 
        encoding: 'utf8',
        timeout: 60000 
      });
      
      this.results.testResults.productionReadiness = {
        status: 'PASSED',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Production readiness tests passed');
    } catch (error) {
      this.results.testResults.productionReadiness = {
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      };
      
      this.results.vulnerabilities.push({
        severity: 'HIGH',
        category: 'PRODUCTION_READINESS',
        description: 'Production readiness validation failed',
        details: error.message
      });
      
      console.log('❌ Production readiness tests failed');
    }
  }

  async validateFirestoreRules() {
    console.log('🔥 Validating Firestore Security Rules...');
    
    try {
      // Check if firestore.rules exists and has proper security measures
      const rulesPath = path.join(process.cwd(), 'firestore.rules');
      
      if (!fs.existsSync(rulesPath)) {
        throw new Error('Firestore rules file not found');
      }
      
      const rulesContent = fs.readFileSync(rulesPath, 'utf8');
      
      // Check for critical security patterns
      const securityChecks = [
        { pattern: /isAuthenticated\(\)/, name: 'Authentication checks' },
        { pattern: /isOwner\(/, name: 'Ownership validation' },
        { pattern: /isAdmin\(\)/, name: 'Admin role checks' },
        { pattern: /allow read, write: if false/, name: 'System-only collections' },
        { pattern: /rateLimits/, name: 'Rate limiting protection' },
        { pattern: /secretCodes/, name: 'Secret code protection' }
      ];
      
      const missingChecks = securityChecks.filter(check => 
        !check.pattern.test(rulesContent)
      );
      
      if (missingChecks.length > 0) {
        this.results.vulnerabilities.push({
          severity: 'HIGH',
          category: 'FIRESTORE_RULES',
          description: 'Missing security patterns in Firestore rules',
          details: missingChecks.map(check => check.name)
        });
      }
      
      this.results.testResults.firestoreRules = {
        status: missingChecks.length === 0 ? 'PASSED' : 'FAILED',
        securityPatterns: securityChecks.length - missingChecks.length,
        missingPatterns: missingChecks.length,
        timestamp: new Date().toISOString()
      };
      
      console.log(`✅ Firestore rules validation: ${securityChecks.length - missingChecks.length}/${securityChecks.length} security patterns found`);
      
    } catch (error) {
      this.results.vulnerabilities.push({
        severity: 'CRITICAL',
        category: 'FIRESTORE_RULES',
        description: 'Firestore rules validation failed',
        details: error.message
      });
      
      console.log('❌ Firestore rules validation failed');
    }
  }

  async checkDependencyVulnerabilities() {
    console.log('📦 Checking Dependencies for Vulnerabilities...');
    
    try {
      const auditOutput = execSync('npm audit --json', { 
        encoding: 'utf8',
        timeout: 30000 
      });
      
      const auditData = JSON.parse(auditOutput);
      
      const highVulnerabilities = auditData.vulnerabilities ? 
        Object.values(auditData.vulnerabilities).filter(vuln => 
          vuln.severity === 'high' || vuln.severity === 'critical'
        ) : [];
      
      if (highVulnerabilities.length > 0) {
        this.results.vulnerabilities.push({
          severity: 'HIGH',
          category: 'DEPENDENCY_VULNERABILITY',
          description: `Found ${highVulnerabilities.length} high/critical dependency vulnerabilities`,
          details: highVulnerabilities.map(vuln => vuln.name)
        });
      }
      
      this.results.testResults.dependencyAudit = {
        status: highVulnerabilities.length === 0 ? 'PASSED' : 'FAILED',
        totalVulnerabilities: auditData.metadata?.vulnerabilities?.total || 0,
        highCriticalVulnerabilities: highVulnerabilities.length,
        timestamp: new Date().toISOString()
      };
      
      console.log(`✅ Dependency audit: ${highVulnerabilities.length} high/critical vulnerabilities found`);
      
    } catch (error) {
      console.log('⚠️ Dependency audit completed with warnings');
      this.results.testResults.dependencyAudit = {
        status: 'WARNING',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  async validateEnvironmentConfig() {
    console.log('⚙️ Validating Environment Configuration...');
    
    const requiredEnvVars = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_STRIPE_PUBLISHABLE_KEY',
      'VITE_SENTRY_DSN'
    ];
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      this.results.vulnerabilities.push({
        severity: 'HIGH',
        category: 'ENVIRONMENT_CONFIG',
        description: 'Missing required environment variables',
        details: missingVars
      });
    }
    
    // Check for production-ready values
    const prodChecks = [
      {
        var: 'VITE_FIREBASE_PROJECT_ID',
        check: (val) => val && val.includes('hive-campus'),
        name: 'Production Firebase project'
      },
      {
        var: 'VITE_STRIPE_PUBLISHABLE_KEY',
        check: (val) => val && val.startsWith('pk_live_'),
        name: 'Live Stripe key'
      }
    ];
    
    const failedProdChecks = prodChecks.filter(check => 
      !check.check(process.env[check.var])
    );
    
    if (failedProdChecks.length > 0) {
      this.results.vulnerabilities.push({
        severity: 'MEDIUM',
        category: 'PRODUCTION_CONFIG',
        description: 'Non-production configuration detected',
        details: failedProdChecks.map(check => check.name)
      });
    }
    
    this.results.testResults.environmentConfig = {
      status: missingVars.length === 0 && failedProdChecks.length === 0 ? 'PASSED' : 'FAILED',
      missingVariables: missingVars.length,
      productionConfigIssues: failedProdChecks.length,
      timestamp: new Date().toISOString()
    };
    
    console.log(`✅ Environment config: ${missingVars.length} missing vars, ${failedProdChecks.length} prod config issues`);
  }

  calculateSecurityScore() {
    const testResults = Object.values(this.results.testResults);
    const passedTests = testResults.filter(result => result.status === 'PASSED').length;
    const totalTests = testResults.length;
    
    let baseScore = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
    
    // Deduct points for vulnerabilities
    const vulnerabilityPenalties = {
      'CRITICAL': 25,
      'HIGH': 15,
      'MEDIUM': 5,
      'LOW': 1
    };
    
    let penalty = 0;
    this.results.vulnerabilities.forEach(vuln => {
      penalty += vulnerabilityPenalties[vuln.severity] || 0;
    });
    
    return Math.max(0, Math.min(100, baseScore - penalty));
  }

  async generateFinalReport() {
    console.log('\n📊 Generating Final Security Report...');
    
    this.results.securityScore = this.calculateSecurityScore();
    
    // Determine overall status
    if (this.results.securityScore >= 90 && this.results.vulnerabilities.filter(v => v.severity === 'CRITICAL').length === 0) {
      this.results.overallStatus = 'PRODUCTION_READY';
      this.results.productionReadiness = true;
    } else if (this.results.securityScore >= 70) {
      this.results.overallStatus = 'NEEDS_MINOR_FIXES';
      this.results.productionReadiness = false;
    } else {
      this.results.overallStatus = 'NEEDS_MAJOR_FIXES';
      this.results.productionReadiness = false;
    }
    
    // Generate recommendations
    this.generateRecommendations();
    
    // Save report to file
    fs.writeFileSync(REPORT_FILE, JSON.stringify(this.results, null, 2));
    
    // Display summary
    this.displaySummary();
  }

  generateRecommendations() {
    if (this.results.vulnerabilities.length === 0) {
      this.results.recommendations.push('✅ No security vulnerabilities detected. System is production ready.');
      return;
    }
    
    const criticalVulns = this.results.vulnerabilities.filter(v => v.severity === 'CRITICAL');
    const highVulns = this.results.vulnerabilities.filter(v => v.severity === 'HIGH');
    
    if (criticalVulns.length > 0) {
      this.results.recommendations.push('🚨 CRITICAL: Fix all critical vulnerabilities before production deployment');
    }
    
    if (highVulns.length > 0) {
      this.results.recommendations.push('⚠️ HIGH: Address high-severity vulnerabilities as soon as possible');
    }
    
    // Category-specific recommendations
    const categories = [...new Set(this.results.vulnerabilities.map(v => v.category))];
    
    categories.forEach(category => {
      switch (category) {
        case 'FIRESTORE_RULES':
          this.results.recommendations.push('🔥 Update Firestore security rules to include missing patterns');
          break;
        case 'DEPENDENCY_VULNERABILITY':
          this.results.recommendations.push('📦 Run npm audit fix to resolve dependency vulnerabilities');
          break;
        case 'ENVIRONMENT_CONFIG':
          this.results.recommendations.push('⚙️ Configure missing environment variables for production');
          break;
      }
    });
  }

  displaySummary() {
    console.log('\n' + '='.repeat(60));
    console.log('🔐 HIVE CAMPUS SECURITY AUDIT SUMMARY');
    console.log('='.repeat(60));
    console.log(`📅 Audit Date: ${this.results.timestamp}`);
    console.log(`🎯 Security Score: ${this.results.securityScore.toFixed(1)}/100`);
    console.log(`📊 Overall Status: ${this.results.overallStatus}`);
    console.log(`🚀 Production Ready: ${this.results.productionReadiness ? 'YES' : 'NO'}`);
    console.log(`🔍 Vulnerabilities Found: ${this.results.vulnerabilities.length}`);
    
    if (this.results.vulnerabilities.length > 0) {
      console.log('\n🚨 VULNERABILITIES BY SEVERITY:');
      const severityCounts = {};
      this.results.vulnerabilities.forEach(vuln => {
        severityCounts[vuln.severity] = (severityCounts[vuln.severity] || 0) + 1;
      });
      
      Object.entries(severityCounts).forEach(([severity, count]) => {
        console.log(`   ${severity}: ${count}`);
      });
    }
    
    console.log('\n📋 RECOMMENDATIONS:');
    this.results.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });
    
    console.log(`\n📄 Full report saved to: ${REPORT_FILE}`);
    console.log('='.repeat(60));
  }
}

// Run the audit
const auditor = new SecurityAuditor();
auditor.runComprehensiveAudit().catch(console.error);
