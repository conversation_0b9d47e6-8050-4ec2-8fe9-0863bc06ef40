"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletConfig = exports.SecureWalletManager = void 0;
const admin = __importStar(require("firebase-admin"));
const functions = __importStar(require("firebase-functions/v1"));
const schemas_1 = require("../validation/schemas");
/**
 * Secure wallet operations with atomic transactions
 */
class SecureWalletManager {
    /**
     * Atomically deduct amount from wallet with race condition protection
     */
    static async deductFromWallet(userId, amount, orderId, description = 'Order payment') {
        // Validate input
        const validatedAmount = (0, schemas_1.validateInput)(schemas_1.WalletAmountSchema, amount);
        if (!userId || !orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID and Order ID are required');
        }
        const walletRef = admin.firestore().collection('wallets').doc(userId);
        const transactionRef = admin.firestore().collection('walletTransactions').doc();
        try {
            const result = await admin.firestore().runTransaction(async (transaction) => {
                // Get current wallet state
                const walletDoc = await transaction.get(walletRef);
                if (!walletDoc.exists) {
                    throw new functions.https.HttpsError('not-found', 'Wallet not found for user');
                }
                const walletData = walletDoc.data();
                const currentBalance = walletData.balance || 0;
                // Check if sufficient balance
                if (currentBalance < validatedAmount) {
                    throw new functions.https.HttpsError('failed-precondition', `Insufficient wallet balance. Available: $${currentBalance.toFixed(2)}, Required: $${validatedAmount.toFixed(2)}`);
                }
                // Check for duplicate transaction (idempotency)
                const existingTransactionQuery = await admin.firestore()
                    .collection('walletTransactions')
                    .where('userId', '==', userId)
                    .where('orderId', '==', orderId)
                    .where('type', '==', 'debit')
                    .limit(1)
                    .get();
                if (!existingTransactionQuery.empty) {
                    throw new functions.https.HttpsError('already-exists', 'Transaction already processed for this order');
                }
                const newBalance = currentBalance - validatedAmount;
                const now = admin.firestore.Timestamp.now();
                // Update wallet balance with version increment for optimistic locking
                transaction.update(walletRef, {
                    balance: newBalance,
                    lastUpdated: now,
                    version: admin.firestore.FieldValue.increment(1)
                });
                // Create transaction record
                const transactionData = {
                    id: transactionRef.id,
                    userId,
                    amount: validatedAmount,
                    type: 'debit',
                    description,
                    orderId,
                    createdAt: now,
                    status: 'completed',
                    metadata: {
                        previousBalance: currentBalance,
                        newBalance,
                        source: 'order_payment'
                    }
                };
                transaction.set(transactionRef, transactionData);
                console.log(`✅ Wallet deduction successful: User ${userId}, Amount $${validatedAmount}, New Balance $${newBalance}`);
                return {
                    success: true,
                    newBalance,
                    transactionId: transactionRef.id
                };
            });
            return result;
        }
        catch (error) {
            console.error('❌ Wallet deduction failed:', error);
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            throw new functions.https.HttpsError('internal', 'Failed to process wallet deduction');
        }
    }
    /**
     * Atomically add amount to wallet (for refunds, bonuses, etc.)
     */
    static async creditWallet(userId, amount, description, orderId, metadata) {
        // Validate input
        const validatedAmount = (0, schemas_1.validateInput)(schemas_1.WalletAmountSchema, amount);
        if (!userId) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID is required');
        }
        const walletRef = admin.firestore().collection('wallets').doc(userId);
        const transactionRef = admin.firestore().collection('walletTransactions').doc();
        try {
            const result = await admin.firestore().runTransaction(async (transaction) => {
                // Get or create wallet
                const walletDoc = await transaction.get(walletRef);
                let currentBalance = 0;
                let walletExists = walletDoc.exists;
                if (walletExists) {
                    const walletData = walletDoc.data();
                    currentBalance = walletData.balance || 0;
                }
                const newBalance = currentBalance + validatedAmount;
                const now = admin.firestore.Timestamp.now();
                // Prevent excessive wallet balances (security measure)
                if (newBalance > 10000) { // $10,000 limit
                    throw new functions.https.HttpsError('failed-precondition', 'Wallet balance would exceed maximum allowed limit');
                }
                // Update or create wallet
                const walletData = {
                    userId,
                    balance: newBalance,
                    lastUpdated: now,
                    version: walletExists ? admin.firestore.FieldValue.increment(1) : 1
                };
                if (walletExists) {
                    transaction.update(walletRef, walletData);
                }
                else {
                    transaction.set(walletRef, Object.assign(Object.assign({}, walletData), { createdAt: now, referralCode: `user${userId.substring(0, 6)}`, usedReferral: false }));
                }
                // Create transaction record
                const transactionData = {
                    id: transactionRef.id,
                    userId,
                    amount: validatedAmount,
                    type: 'credit',
                    description,
                    orderId,
                    createdAt: now,
                    status: 'completed',
                    metadata: Object.assign({ previousBalance: currentBalance, newBalance }, metadata)
                };
                transaction.set(transactionRef, transactionData);
                console.log(`✅ Wallet credit successful: User ${userId}, Amount $${validatedAmount}, New Balance $${newBalance}`);
                return {
                    success: true,
                    newBalance,
                    transactionId: transactionRef.id
                };
            });
            return result;
        }
        catch (error) {
            console.error('❌ Wallet credit failed:', error);
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            throw new functions.https.HttpsError('internal', 'Failed to process wallet credit');
        }
    }
    /**
     * Get wallet balance securely
     */
    static async getWalletBalance(userId) {
        if (!userId) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID is required');
        }
        try {
            const walletDoc = await admin.firestore()
                .collection('wallets')
                .doc(userId)
                .get();
            if (!walletDoc.exists) {
                return 0;
            }
            const walletData = walletDoc.data();
            return walletData.balance || 0;
        }
        catch (error) {
            console.error('❌ Error getting wallet balance:', error);
            throw new functions.https.HttpsError('internal', 'Failed to get wallet balance');
        }
    }
    /**
     * Validate wallet transaction for security
     */
    static validateTransaction(amount, userId, orderId) {
        // Validate amount
        if (amount <= 0 || amount > 1000) {
            throw new functions.https.HttpsError('invalid-argument', 'Transaction amount must be between $0.01 and $1000');
        }
        // Validate user ID format
        if (!userId || userId.length < 10 || !/^[a-zA-Z0-9]+$/.test(userId)) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid user ID format');
        }
        // Validate order ID format if provided
        if (orderId && (orderId.length < 5 || !/^[a-zA-Z0-9_-]+$/.test(orderId))) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid order ID format');
        }
    }
    /**
     * Get transaction history for a user (with pagination)
     */
    static async getTransactionHistory(userId, limit = 20, startAfter) {
        if (!userId) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID is required');
        }
        try {
            let query = admin.firestore()
                .collection('walletTransactions')
                .where('userId', '==', userId)
                .orderBy('createdAt', 'desc')
                .limit(limit + 1); // Get one extra to check if there are more
            if (startAfter) {
                const startAfterDoc = await admin.firestore()
                    .collection('walletTransactions')
                    .doc(startAfter)
                    .get();
                if (startAfterDoc.exists) {
                    query = query.startAfter(startAfterDoc);
                }
            }
            const snapshot = await query.get();
            const transactions = snapshot.docs.slice(0, limit).map(doc => (Object.assign({ id: doc.id }, doc.data())));
            const hasMore = snapshot.docs.length > limit;
            const lastVisible = transactions.length > 0 ? transactions[transactions.length - 1].id : undefined;
            return {
                transactions,
                hasMore,
                lastVisible
            };
        }
        catch (error) {
            console.error('❌ Error getting transaction history:', error);
            throw new functions.https.HttpsError('internal', 'Failed to get transaction history');
        }
    }
}
exports.SecureWalletManager = SecureWalletManager;
// Export wallet configuration
exports.WalletConfig = {
    MAX_BALANCE: 10000,
    MIN_TRANSACTION: 0.01,
    MAX_TRANSACTION: 1000,
    TRANSACTION_TIMEOUT: 30000 // 30 seconds
};
