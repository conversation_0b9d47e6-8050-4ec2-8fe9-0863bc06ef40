import {
  collection,
  doc,
  getDoc,
  setDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  serverTimestamp,
  updateDoc,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { firestore, auth } from './config';
import { Chat, Message } from './types';
import { uploadChatImage } from './uploads';
import { firebaseErrorHandler, withFirebaseErrorHandling } from '../utils/firebaseErrorHandler';
import { retryWithBackoff, isBlockedByClient } from '../utils/networkUtils';

// Specialized wrapper for Firebase write operations that handles blocking
async function safeFirebaseWrite<T>(
  operation: () => Promise<T>,
  operationName: string = 'write'
): Promise<T> {
  try {
    return await retryWithBackoff(
      operation,
      2, // Max 2 retries for write operations
      1500, // 1.5 second base delay
      5000 // Max 5 second delay
    );
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (isBlockedByClient(errorMessage)) {
      // Create a user-friendly error for blocked requests
      const blockedError = new Error(
        `Unable to ${operationName} due to browser blocking. This is usually caused by:\n\n` +
        '• Ad blockers or browser extensions\n' +
        '• Corporate firewall settings\n' +
        '• Browser security settings\n\n' +
        'Try:\n' +
        '• Disabling ad blockers for this site\n' +
        '• Using incognito/private browsing mode\n' +
        '• Checking with your network administrator'
      );

      // Log the technical details for debugging
      console.error(`Firebase ${operationName} blocked:`, {
        operation: operationName,
        error: errorMessage,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });

      throw blockedError;
    }

    // Re-throw other errors with context
    throw new Error(`Failed to ${operationName}: ${errorMessage}`);
  }
}

// Create a notification for new chat messages
const createChatNotification = async (
  receiverId: string,
  senderId: string,
  chatId: string,
  messageText: string
) => {
  try {
    // Get sender's name
    const senderDoc = await getDoc(doc(firestore, 'users', senderId));
    const senderName = senderDoc.exists() ? senderDoc.data().name : 'Someone';

    // Create notification in user's notifications subcollection
    const notificationRef = collection(firestore, `users/${receiverId}/notifications`);
    await addDoc(notificationRef, {
      type: 'new_chat_message',
      title: `New message from ${senderName}`,
      message: messageText.length > 50 ? messageText.substring(0, 50) + '...' : messageText,
      icon: '/icons/icon-192.png',
      createdAt: serverTimestamp(),
      read: false,
      link: `/messages?chat=${chatId}`,
      priority: 'normal',
      actionRequired: false,
      expiresAt: Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // 24 hours
      metadata: {},
      chatId,
      senderId,
      senderName
    });

    // Also send push notification if user has FCM token
    try {
      const tokenDoc = await getDoc(doc(firestore, `users/${receiverId}/fcmTokens/web`));
      if (tokenDoc.exists() && tokenDoc.data()?.active && tokenDoc.data()?.token) {
        // Import messaging from config
        const { messaging } = await import('../firebase/config');
        if (messaging) {
          const payload = {
            token: tokenDoc.data()!.token,
            notification: {
              title: `New message from ${senderName}`,
              body: messageText.length > 50 ? messageText.substring(0, 50) + '...' : messageText,
              imageUrl: '/icons/icon-192.png'
            },
            data: {
              type: 'new_chat_message',
              link: `/messages?chat=${chatId}`,
              chatId: chatId,
              senderId: senderId,
              requireInteraction: 'false'
            },
            webpush: {
              headers: {
                TTL: '86400',
                Urgency: 'normal'
              },
              notification: {
                icon: '/icons/icon-192.png',
                badge: '/icons/icon-96.png',
                tag: 'new_chat_message',
                requireInteraction: false,
                actions: [
                  { action: 'view', title: 'Reply' },
                  { action: 'dismiss', title: 'Dismiss' }
                ],
                vibrate: [200, 100, 200]
              }
            }
          };

          // Note: This would need to be done via a Cloud Function in production
          // For now, we'll just create the in-app notification
          console.log('Push notification payload prepared:', payload);
        }
      }
    } catch (pushError) {
      console.warn('Could not send push notification for chat message:', pushError);
    }
  } catch (error) {
    console.error('Error creating chat notification:', error);
  }
};

// Generate chat ID from two user IDs (sorted lexicographically)
export const generateChatId = (userId1: string, userId2: string): string => {
  return `chat_${[userId1, userId2].sort().join('_')}`;
};

// Create or get a chat between two users
export const createOrGetChat = async (
  otherUserId: string,
  listingContext?: {
    listingId: string;
    listingTitle: string;
    listingImageURL?: string;
  }
) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    if (currentUser.uid === otherUserId) {
      throw new Error('Cannot create chat with yourself');
    }

    const chatId = generateChatId(currentUser.uid, otherUserId);

    const chatRef = doc(firestore, 'chats', chatId);
    const chatDoc = await getDoc(chatRef);

    if (chatDoc.exists()) {
      // Chat already exists, return it
      return {
        success: true,
        data: {
          chatId,
          chat: { id: chatId, ...chatDoc.data() } as Chat
        }
      };
    } else {
      // Create new chat
      const newChat: Omit<Chat, 'id'> = {
        participants: [currentUser.uid, otherUserId],
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      };

      // Add listing context if provided, but filter out undefined values
      if (listingContext) {
        const cleanListingContext: any = {
          listingId: listingContext.listingId,
          listingTitle: listingContext.listingTitle,
        };

        // Only add listingImageURL if it's not undefined
        if (listingContext.listingImageURL !== undefined) {
          cleanListingContext.listingImageURL = listingContext.listingImageURL;
        }

        newChat.listingContext = cleanListingContext;
      }

      await setDoc(chatRef, newChat);

      return {
        success: true,
        data: {
          chatId,
          chat: { id: chatId, ...newChat } as Chat
        }
      };
    }
  } catch (error: unknown) {
    console.error('Error creating/getting chat:', error);
    throw error;
  }
};

// Send a text message in a chat
export const sendMessage = async (
  chatId: string,
  text: string,
  receiverId: string,
  listingContext?: {
    listingId: string;
    title: string;
    imageURL?: string;
  }
) => {
  return withFirebaseErrorHandling(async () => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Add message to the messages subcollection
    const messagesRef = collection(firestore, `chats/${chatId}/messages`);
    const now = Timestamp.now();
    const newMessage: Omit<Message, 'id'> = {
      senderId: currentUser.uid,
      receiverId,
      text,
      type: 'text',
      createdAt: now,
      read: false,
    };

    // Add listing context if provided, but filter out undefined values
    if (listingContext) {
      const cleanListingContext: any = {
        listingId: listingContext.listingId,
        title: listingContext.title,
      };

      // Only add imageURL if it's not undefined
      if (listingContext.imageURL !== undefined) {
        cleanListingContext.imageURL = listingContext.imageURL;
      }

      newMessage.listingContext = cleanListingContext;
    }

    const messageDoc = await safeFirebaseWrite(
      () => addDoc(messagesRef, newMessage),
      'send message'
    );

    // Update the chat's lastMessage and updatedAt
    const chatRef = doc(firestore, 'chats', chatId);
    await safeFirebaseWrite(
      () => updateDoc(chatRef, {
        lastMessage: {
          text,
          type: 'text',
          senderId: currentUser.uid,
          createdAt: now
        },
        updatedAt: now
      }),
      'update chat'
    );

    // Create notification for the receiver (non-critical, so use regular error handling)
    try {
      await createChatNotification(receiverId, currentUser.uid, chatId, text);
    } catch (error) {
      console.warn('Failed to create notification:', error);
      // Don't fail the entire operation if notification fails
    }

    return {
      success: true,
      data: {
        messageId: messageDoc.id,
        message: { id: messageDoc.id, ...newMessage } as Message
      }
    };
  });
};

// Send an image message in a chat
export const sendImageMessage = async (
  chatId: string,
  imageFile: File,
  receiverId: string,
  listingContext?: {
    listingId: string;
    title: string;
    imageURL?: string;
  }
) => {
  try {
    console.log('sendImageMessage called with:', {
      chatId,
      fileName: imageFile.name,
      fileSize: imageFile.size,
      receiverId
    });

    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('No authenticated user in sendImageMessage');
      throw new Error('User not authenticated');
    }

    console.log('User authenticated, starting image upload...');

    // Upload the image first
    const uploadResult = await uploadChatImage(imageFile);
    console.log('Image upload completed in sendImageMessage:', uploadResult);

    // Add message to the messages subcollection
    const messagesRef = collection(firestore, `chats/${chatId}/messages`);
    const now = Timestamp.now();
    const newMessage: Omit<Message, 'id'> = {
      senderId: currentUser.uid,
      receiverId,
      type: 'image',
      createdAt: now,
      read: false,
      imageData: {
        url: uploadResult.downloadURL,
        fileName: uploadResult.fileName,
        fileSize: uploadResult.fileSize,
        fileType: uploadResult.fileType,
        storagePath: uploadResult.storagePath
      }
    };

    // Add listing context if provided, but filter out undefined values
    if (listingContext) {
      const cleanListingContext: any = {
        listingId: listingContext.listingId,
        title: listingContext.title,
      };

      // Only add imageURL if it's not undefined
      if (listingContext.imageURL !== undefined) {
        cleanListingContext.imageURL = listingContext.imageURL;
      }

      newMessage.listingContext = cleanListingContext;
    }

    const messageDoc = await safeFirebaseWrite(
      () => addDoc(messagesRef, newMessage),
      'send image message'
    );

    // Update the chat's lastMessage and updatedAt
    const chatRef = doc(firestore, 'chats', chatId);
    await safeFirebaseWrite(
      () => updateDoc(chatRef, {
        lastMessage: {
          type: 'image',
          senderId: currentUser.uid,
          createdAt: now
        },
        updatedAt: now
      }),
      'update chat with image'
    );

    // Create notification for the receiver (non-critical)
    try {
      await createChatNotification(receiverId, currentUser.uid, chatId, '📷 Sent an image');
    } catch (error) {
      console.warn('Failed to create image notification:', error);
    }

    return {
      success: true,
      data: {
        messageId: messageDoc.id,
        message: { id: messageDoc.id, ...newMessage } as Message
      }
    };
  } catch (error: unknown) {
    console.error('Error sending image message:', error);
    throw error;
  }
};

// Get messages from a chat (for initial load)
export const getMessages = async (
  chatId: string,
  limitCount: number = 50
) => {
  try {
    const messagesRef = collection(firestore, `chats/${chatId}/messages`);
    const messagesQuery = query(
      messagesRef,
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(messagesQuery);
    const messages: Message[] = [];

    snapshot.forEach((doc) => {
      messages.push({
        id: doc.id,
        ...doc.data()
      } as Message);
    });

    // Reverse to get chronological order (oldest first)
    messages.reverse();

    return {
      success: true,
      data: { messages }
    };
  } catch (error: unknown) {
    console.error('Error getting messages:', error);
    throw error;
  }
};

// Get all chats for a user
export const getChats = async (userId: string) => {
  try {
    const chatsRef = collection(firestore, 'chats');
    const chatsQuery = query(
      chatsRef,
      where('participants', 'array-contains', userId),
      orderBy('updatedAt', 'desc')
    );

    const snapshot = await getDocs(chatsQuery);
    const chats: Chat[] = [];

    snapshot.forEach((doc) => {
      chats.push({
        id: doc.id,
        ...doc.data()
      } as Chat);
    });

    return {
      success: true,
      data: { chats }
    };
  } catch (error: unknown) {
    console.error('Error getting chats:', error);
    throw error;
  }
};

// Mark all messages in a chat as read
export const markChatAsRead = async (chatId: string) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const messagesRef = collection(firestore, `chats/${chatId}/messages`);
    const unreadQuery = query(
      messagesRef,
      where('receiverId', '==', currentUser.uid),
      where('read', '==', false)
    );

    const snapshot = await getDocs(unreadQuery);
    const updatePromises: Promise<void>[] = [];

    snapshot.forEach((doc) => {
      updatePromises.push(
        safeFirebaseWrite(
          () => updateDoc(doc.ref, { read: true }),
          'mark message as read'
        )
      );
    });

    await Promise.all(updatePromises);

    return {
      success: true,
      data: { updatedCount: snapshot.size }
    };
  } catch (error: unknown) {
    console.error('Error marking chat as read:', error);
    throw error;
  }
};

// Real-time subscription to messages in a chat
export const subscribeToMessages = (
  chatId: string,
  callback: (messages: Message[]) => void,
  onError?: (error: Error) => void
) => {
  const messagesRef = collection(firestore, `chats/${chatId}/messages`);
  const messagesQuery = query(messagesRef, orderBy('createdAt', 'asc'));

  // Add connection state tracking
  let isConnected = true;

  const unsubscribe = onSnapshot(
    messagesQuery,
    {
      includeMetadataChanges: false, // Reduce unnecessary updates
    },
    (snapshot) => {
      try {
        if (!isConnected) return; // Ignore updates if disconnected

        const messages: Message[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          // Ensure createdAt is properly handled
          if (data.createdAt && typeof data.createdAt.toDate === 'function') {
            data.createdAt = data.createdAt.toDate();
          }
          messages.push({
            id: doc.id,
            ...data
          } as Message);
        });
        callback(messages);
      } catch (error) {
        console.error('Error processing messages snapshot:', error);
        onError?.(error as Error);
      }
    },
    (error) => {
      console.error('Error in messages subscription:', error);
      isConnected = false;

      // Handle specific Firestore errors
      if (error.code === 'permission-denied') {
        onError?.(new Error('Permission denied. Please check your access rights.'));
      } else if (error.code === 'unavailable') {
        onError?.(new Error('Service temporarily unavailable. Please try again.'));
      } else {
        onError?.(new Error('Connection error. Please check your internet connection.'));
      }
    }
  );

  // Return enhanced unsubscribe function
  return () => {
    isConnected = false;
    unsubscribe();
  };
};

// Real-time subscription to user's chats
export const subscribeToChats = (
  userId: string,
  callback: (chats: Chat[]) => void,
  onError?: (error: Error) => void
) => {
  const chatsRef = collection(firestore, 'chats');
  const chatsQuery = query(
    chatsRef,
    where('participants', 'array-contains', userId),
    orderBy('updatedAt', 'desc')
  );

  // Add connection state tracking
  let isConnected = true;

  const unsubscribe = onSnapshot(
    chatsQuery,
    {
      includeMetadataChanges: false, // Reduce unnecessary updates
    },
    (snapshot) => {
      try {
        if (!isConnected) return; // Ignore updates if disconnected

        const chats: Chat[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          // Ensure timestamps are properly handled
          if (data.createdAt && typeof data.createdAt.toDate === 'function') {
            data.createdAt = data.createdAt.toDate();
          }
          if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
            data.updatedAt = data.updatedAt.toDate();
          }
          chats.push({
            id: doc.id,
            ...data
          } as Chat);
        });
        callback(chats);
      } catch (error) {
        console.error('Error processing chats snapshot:', error);
        onError?.(error as Error);
      }
    },
    (error) => {
      console.error('Error in chats subscription:', error);
      isConnected = false;

      // Handle specific Firestore errors
      if (error.code === 'permission-denied') {
        onError?.(new Error('Permission denied. Please check your access rights.'));
      } else if (error.code === 'unavailable') {
        onError?.(new Error('Service temporarily unavailable. Please try again.'));
      } else {
        onError?.(new Error('Connection error. Please check your internet connection.'));
      }
    }
  );

  // Return enhanced unsubscribe function
  return () => {
    isConnected = false;
    unsubscribe();
  };
};