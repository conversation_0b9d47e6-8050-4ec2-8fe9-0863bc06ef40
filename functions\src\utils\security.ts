import * as admin from 'firebase-admin';
import * as bcrypt from 'bcrypt';
import { randomBytes } from 'crypto';
import * as functions from 'firebase-functions/v1';

// Security utilities for Hive Campus

// Constants for security configuration
const BCRYPT_ROUNDS = 12; // High security rounds for bcrypt
const PIN_LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes lockout
const MAX_PIN_ATTEMPTS = 5;
const SECRET_CODE_EXPIRY = 10 * 60 * 1000; // 10 minutes for secret codes

// Rate limiting interface
interface RateLimitAttempt {
  count: number;
  firstAttempt: admin.firestore.Timestamp;
  lastAttempt: admin.firestore.Timestamp;
  lockedUntil?: admin.firestore.Timestamp;
}

/**
 * Secure PIN management with bcrypt and salt
 */
export class SecurePinManager {
  /**
   * Hash a PIN using bcrypt with salt
   */
  static async hashPin(pin: string): Promise<string> {
    try {
      // Add application-specific salt before hashing
      const saltedPin = pin + process.env.HIVE_PIN_SALT || 'HIVE_CAMPUS_2024_SECURE';
      return await bcrypt.hash(saltedPin, BCRYPT_ROUNDS);
    } catch (error) {
      console.error('Error hashing PIN:', error);
      throw new functions.https.HttpsError('internal', 'Failed to secure PIN');
    }
  }

  /**
   * Verify a PIN against stored hash
   */
  static async verifyPin(pin: string, hashedPin: string): Promise<boolean> {
    try {
      const saltedPin = pin + process.env.HIVE_PIN_SALT || 'HIVE_CAMPUS_2024_SECURE';
      return await bcrypt.compare(saltedPin, hashedPin);
    } catch (error) {
      console.error('Error verifying PIN:', error);
      return false;
    }
  }

  /**
   * Check and update rate limiting for PIN attempts
   */
  static async checkPinRateLimit(userId: string, clientIP?: string): Promise<boolean> {
    const identifier = `pin_${userId}_${clientIP || 'unknown'}`;
    const rateLimitRef = admin.firestore().collection('rateLimits').doc(identifier);
    
    try {
      const result = await admin.firestore().runTransaction(async (transaction) => {
        const doc = await transaction.get(rateLimitRef);
        const now = admin.firestore.Timestamp.now();
        
        if (!doc.exists) {
          // First attempt
          transaction.set(rateLimitRef, {
            count: 1,
            firstAttempt: now,
            lastAttempt: now
          } as RateLimitAttempt);
          return true;
        }
        
        const data = doc.data() as RateLimitAttempt;
        
        // Check if currently locked out
        if (data.lockedUntil && data.lockedUntil.toMillis() > now.toMillis()) {
          return false; // Still locked out
        }
        
        // Reset if window has passed (1 hour)
        const windowStart = now.toMillis() - (60 * 60 * 1000);
        if (data.firstAttempt.toMillis() < windowStart) {
          transaction.set(rateLimitRef, {
            count: 1,
            firstAttempt: now,
            lastAttempt: now
          } as RateLimitAttempt);
          return true;
        }
        
        // Check if max attempts exceeded
        if (data.count >= MAX_PIN_ATTEMPTS) {
          const lockoutUntil = admin.firestore.Timestamp.fromMillis(
            now.toMillis() + PIN_LOCKOUT_DURATION
          );
          transaction.update(rateLimitRef, {
            lockedUntil: lockoutUntil,
            lastAttempt: now
          });
          return false;
        }
        
        // Increment attempt count
        transaction.update(rateLimitRef, {
          count: data.count + 1,
          lastAttempt: now
        });
        
        return true;
      });
      
      return result;
    } catch (error) {
      console.error('Error checking PIN rate limit:', error);
      // Fail secure - deny access on error
      return false;
    }
  }

  /**
   * Reset rate limiting after successful PIN verification
   */
  static async resetPinRateLimit(userId: string, clientIP?: string): Promise<void> {
    const identifier = `pin_${userId}_${clientIP || 'unknown'}`;
    const rateLimitRef = admin.firestore().collection('rateLimits').doc(identifier);
    
    try {
      await rateLimitRef.delete();
    } catch (error) {
      console.error('Error resetting PIN rate limit:', error);
      // Non-critical error, don't throw
    }
  }
}

/**
 * Secure code generation for delivery confirmation
 */
export class SecureCodeGenerator {
  /**
   * Generate cryptographically secure 6-digit hex code
   */
  static generateSecretCode(): string {
    return randomBytes(3).toString('hex').toUpperCase();
  }

  /**
   * Store secret code with expiration
   */
  static async storeSecretCode(orderId: string, code: string): Promise<void> {
    const expiresAt = admin.firestore.Timestamp.fromMillis(
      Date.now() + SECRET_CODE_EXPIRY
    );
    
    await admin.firestore().collection('secretCodes').doc(orderId).set({
      code,
      orderId,
      createdAt: admin.firestore.Timestamp.now(),
      expiresAt,
      used: false
    });
  }

  /**
   * Verify and consume secret code
   */
  static async verifySecretCode(orderId: string, providedCode: string): Promise<boolean> {
    const codeRef = admin.firestore().collection('secretCodes').doc(orderId);
    
    try {
      const result = await admin.firestore().runTransaction(async (transaction) => {
        const doc = await transaction.get(codeRef);
        
        if (!doc.exists) {
          return false;
        }
        
        const data = doc.data();
        const now = admin.firestore.Timestamp.now();
        
        // Check if expired
        if (data?.expiresAt && data.expiresAt.toMillis() < now.toMillis()) {
          return false;
        }
        
        // Check if already used
        if (data?.used) {
          return false;
        }
        
        // Check if code matches (case-insensitive)
        if (data?.code?.toUpperCase() !== providedCode.toUpperCase()) {
          return false;
        }
        
        // Mark as used
        transaction.update(codeRef, {
          used: true,
          usedAt: now
        });
        
        return true;
      });
      
      return result;
    } catch (error) {
      console.error('Error verifying secret code:', error);
      return false;
    }
  }
}

/**
 * General rate limiting utility
 */
export class RateLimiter {
  /**
   * Check rate limit for any action
   */
  static async checkRateLimit(
    identifier: string,
    action: string,
    maxAttempts: number = 10,
    windowMs: number = 15 * 60 * 1000 // 15 minutes
  ): Promise<boolean> {
    const rateLimitId = `${action}_${identifier}`;
    const rateLimitRef = admin.firestore().collection('rateLimits').doc(rateLimitId);
    
    try {
      const result = await admin.firestore().runTransaction(async (transaction) => {
        const doc = await transaction.get(rateLimitRef);
        const now = admin.firestore.Timestamp.now();
        
        if (!doc.exists) {
          transaction.set(rateLimitRef, {
            count: 1,
            firstAttempt: now,
            lastAttempt: now
          });
          return true;
        }
        
        const data = doc.data() as RateLimitAttempt;
        const windowStart = now.toMillis() - windowMs;
        
        // Reset if window has passed
        if (data.firstAttempt.toMillis() < windowStart) {
          transaction.set(rateLimitRef, {
            count: 1,
            firstAttempt: now,
            lastAttempt: now
          });
          return true;
        }
        
        // Check if max attempts exceeded
        if (data.count >= maxAttempts) {
          return false;
        }
        
        // Increment count
        transaction.update(rateLimitRef, {
          count: data.count + 1,
          lastAttempt: now
        });
        
        return true;
      });
      
      return result;
    } catch (error) {
      console.error('Error checking rate limit:', error);
      return false; // Fail secure
    }
  }
}

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Remove HTML tags and dangerous characters
   */
  static sanitizeText(input: string): string {
    return input
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  /**
   * Sanitize and validate email
   */
  static sanitizeEmail(email: string): string {
    return email.toLowerCase().trim();
  }

  /**
   * Validate and sanitize numeric input
   */
  static sanitizeNumber(input: unknown, min: number = 0, max: number = Number.MAX_SAFE_INTEGER): number {
    const num = Number(input);
    if (isNaN(num) || num < min || num > max) {
      throw new Error(`Invalid number: must be between ${min} and ${max}`);
    }
    return num;
  }
}

// Export security configuration constants
export const SecurityConfig = {
  BCRYPT_ROUNDS,
  PIN_LOCKOUT_DURATION,
  MAX_PIN_ATTEMPTS,
  SECRET_CODE_EXPIRY
};
