/**
 * 🛠️ MARKETPLACE TEST HELPERS
 * Utility functions for marketplace flow testing
 */

import { 
  doc, setDoc, getDoc, updateDoc, deleteDoc, collection, addDoc, query, where, getDocs,
  serverTimestamp 
} from 'firebase/firestore';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut } from 'firebase/auth';
import { httpsCallable } from 'firebase/functions';
import { TestDataFactory } from './test-data-factory.js';

export class MarketplaceTestHelpers {
  constructor(db, functions, auth) {
    this.db = db;
    this.functions = functions;
    this.auth = auth;
    this.createdUsers = [];
    this.createdDocuments = [];
  }

  /**
   * Create test users for scenarios
   */
  async createTestUsers() {
    const testData = TestDataFactory.generateTestData();
    const users = {};

    for (const [role, userData] of Object.entries(testData.users)) {
      try {
        // Create Firebase Auth user
        const userCredential = await createUserWithEmailAndPassword(
          this.auth,
          userData.email,
          userData.password
        );

        const user = userCredential.user;
        
        // Create user profile in Firestore
        await setDoc(doc(this.db, 'users', user.uid), {
          uid: user.uid,
          email: userData.email,
          displayName: userData.displayName,
          role: userData.role,
          ...userData.profile,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          isTestUser: true
        });

        users[role] = {
          uid: user.uid,
          email: userData.email,
          displayName: userData.displayName,
          role: userData.role
        };

        this.createdUsers.push(user.uid);
        this.createdDocuments.push({ collection: 'users', id: user.uid });

        console.log(`✅ Created test ${role}: ${userData.email}`);
      } catch (error) {
        console.error(`❌ Failed to create test ${role}:`, error);
        throw error;
      }
    }

    return users;
  }

  /**
   * Sign in a test user
   */
  async signInUser(user) {
    const testData = TestDataFactory.generateTestData();
    const userData = Object.values(testData.users).find(u => u.email === user.email);
    
    if (!userData) {
      throw new Error(`Test user data not found for ${user.email}`);
    }

    await signInWithEmailAndPassword(this.auth, user.email, userData.password);
    console.log(`🔐 Signed in as: ${user.email}`);
  }

  /**
   * Create a test listing
   */
  async createListing(listingData) {
    const listingRef = doc(collection(this.db, 'listings'));
    const listingId = listingRef.id;

    const listing = {
      id: listingId,
      ...listingData,
      sellerId: this.auth.currentUser?.uid,
      sellerName: this.auth.currentUser?.displayName,
      sellerEmail: this.auth.currentUser?.email,
      status: 'active',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      isTestListing: true
    };

    await setDoc(listingRef, listing);
    this.createdDocuments.push({ collection: 'listings', id: listingId });

    return listingId;
  }

  /**
   * Get a listing by ID
   */
  async getListing(listingId) {
    const listingDoc = await getDoc(doc(this.db, 'listings', listingId));
    return listingDoc.exists() ? listingDoc.data() : null;
  }

  /**
   * Add wallet balance to a user
   */
  async addWalletBalance(userId, amount) {
    const walletRef = doc(this.db, 'wallets', userId);
    
    // Check if wallet exists
    const walletDoc = await getDoc(walletRef);
    const currentBalance = walletDoc.exists() ? walletDoc.data().balance || 0 : 0;
    
    const walletData = {
      userId,
      balance: currentBalance + amount,
      lastUpdated: serverTimestamp(),
      referralCode: `test${userId.substring(0, 6)}`,
      usedReferral: false,
      isTestWallet: true
    };

    if (!walletDoc.exists()) {
      walletData.createdAt = serverTimestamp();
    }

    await setDoc(walletRef, walletData, { merge: true });
    this.createdDocuments.push({ collection: 'wallets', id: userId });

    // Create transaction record
    const transactionRef = doc(collection(this.db, 'walletTransactions'));
    await setDoc(transactionRef, {
      id: transactionRef.id,
      userId,
      amount,
      type: 'credit',
      description: 'Test wallet credit',
      createdAt: serverTimestamp(),
      status: 'completed',
      isTestTransaction: true
    });
    this.createdDocuments.push({ collection: 'walletTransactions', id: transactionRef.id });

    console.log(`💰 Added $${amount} to wallet for user: ${userId}`);
  }

  /**
   * Create checkout session
   */
  async createCheckoutSession(listingId, useWalletBalance = false, orderDetails = {}) {
    const createCheckoutSessionFn = httpsCallable(this.functions, 'createCheckoutSession');
    
    const response = await createCheckoutSessionFn({
      listingId,
      useWalletBalance,
      orderDetails
    });

    return response.data;
  }

  /**
   * Simulate Stripe webhook
   */
  async simulateStripeWebhook(sessionId, listingId) {
    // Create a mock webhook event
    const webhookData = {
      type: 'checkout.session.completed',
      data: {
        object: {
          id: sessionId,
          payment_status: 'paid',
          payment_intent: `pi_test_${Math.random().toString(36).substring(2, 15)}`,
          metadata: {
            listingId,
            buyerId: this.auth.currentUser?.uid,
            sessionId
          }
        }
      }
    };

    // Call the webhook function directly
    const stripeWebhookFn = httpsCallable(this.functions, 'stripeWebhook');
    await stripeWebhookFn(webhookData);

    console.log(`🔗 Simulated Stripe webhook for session: ${sessionId}`);
  }

  /**
   * Get order by session ID
   */
  async getOrderBySessionId(sessionId) {
    const ordersQuery = query(
      collection(this.db, 'orders'),
      where('stripeSessionId', '==', sessionId)
    );
    
    const querySnapshot = await getDocs(ordersQuery);
    
    if (querySnapshot.empty) {
      return null;
    }

    return querySnapshot.docs[0].data();
  }

  /**
   * Get order by ID
   */
  async getOrder(orderId) {
    const orderDoc = await getDoc(doc(this.db, 'orders', orderId));
    return orderDoc.exists() ? orderDoc.data() : null;
  }

  /**
   * Generate shipping label
   */
  async generateShippingLabel(orderId) {
    const generateLabelFn = httpsCallable(this.functions, 'generateShippingLabel');
    
    const response = await generateLabelFn({
      orderId,
      rateId: 'test_rate_123' // Mock rate ID
    });

    return response.data;
  }

  /**
   * Mark item as shipped
   */
  async markItemShipped(orderId, trackingNumber) {
    const orderRef = doc(this.db, 'orders', orderId);
    
    await updateDoc(orderRef, {
      status: 'shipped',
      shippedAt: serverTimestamp(),
      trackingNumber,
      updatedAt: serverTimestamp()
    });

    console.log(`🚛 Marked order ${orderId} as shipped`);
  }

  /**
   * Mark order as delivered
   */
  async markOrderDelivered(orderId) {
    const orderRef = doc(this.db, 'orders', orderId);
    
    await updateDoc(orderRef, {
      status: 'delivered',
      deliveredAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log(`📦 Marked order ${orderId} as delivered`);
  }

  /**
   * Release funds with secret code
   */
  async releaseFundsWithCode(orderId, secretCode) {
    const releaseFundsFn = httpsCallable(this.functions, 'releaseFundsWithCode');
    
    const response = await releaseFundsFn({
      orderId,
      secretCode
    });

    return response.data;
  }

  /**
   * Request return
   */
  async requestReturn(orderId, reason) {
    const requestReturnFn = httpsCallable(this.functions, 'requestReturn');
    
    const response = await requestReturnFn({
      orderId,
      reason
    });

    return response.data;
  }

  /**
   * Generate return label
   */
  async generateReturnLabel(orderId) {
    // Mock return label generation
    const mockLabel = {
      success: true,
      label: {
        trackingNumber: `1Z999AA1RETURN${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
        labelUrl: 'https://shippo.com/test-return-label.pdf',
        carrier: 'USPS'
      }
    };

    // Update order with return label
    const orderRef = doc(this.db, 'orders', orderId);
    await updateDoc(orderRef, {
      returnLabel: mockLabel.label,
      returnTrackingNumber: mockLabel.label.trackingNumber,
      returnLabelGeneratedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return mockLabel;
  }

  /**
   * Confirm return and process refund
   */
  async confirmReturnAndRefund(orderId, outcome) {
    const orderRef = doc(this.db, 'orders', orderId);
    
    if (outcome === 'approve_refund') {
      await updateDoc(orderRef, {
        status: 'refunded',
        refundedAt: serverTimestamp(),
        refundAmount: 550, // Mock refund amount
        returnConfirmed: true,
        updatedAt: serverTimestamp()
      });
    }

    return { success: true };
  }

  /**
   * Cleanup test data
   */
  async cleanupTestData() {
    console.log('🧹 Starting test data cleanup...');

    // Delete created documents
    for (const docInfo of this.createdDocuments) {
      try {
        await deleteDoc(doc(this.db, docInfo.collection, docInfo.id));
      } catch (error) {
        console.warn(`⚠️ Failed to delete ${docInfo.collection}/${docInfo.id}:`, error);
      }
    }

    // Sign out current user
    if (this.auth.currentUser) {
      await signOut(this.auth);
    }

    console.log('✅ Test data cleanup completed');
  }

  /**
   * Create test order for returns testing
   */
  async createTestOrderForReturns() {
    const orderRef = doc(collection(this.db, 'orders'));
    const orderId = orderRef.id;

    const order = {
      id: orderId,
      listingId: 'test_listing_returns',
      buyerId: this.auth.currentUser?.uid,
      sellerId: 'test_seller_returns',
      status: 'delivered',
      totalAmount: 100,
      deliveredAt: serverTimestamp(),
      returnEligibleUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      isTestOrder: true
    };

    await setDoc(orderRef, order);
    this.createdDocuments.push({ collection: 'orders', id: orderId });

    return order;
  }

  /**
   * Create test order for auto-release testing
   */
  async createTestOrderForAutoRelease() {
    const orderRef = doc(collection(this.db, 'orders'));
    const orderId = orderRef.id;

    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);

    const order = {
      id: orderId,
      listingId: 'test_listing_autorelease',
      buyerId: 'test_buyer_autorelease',
      sellerId: 'test_seller_autorelease',
      status: 'delivered',
      totalAmount: 75,
      deliveredAt: threeDaysAgo,
      autoReleaseDate: threeDaysAgo,
      fundsReleased: false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      isTestOrder: true
    };

    await setDoc(orderRef, order);
    this.createdDocuments.push({ collection: 'orders', id: orderId });

    return order;
  }

  /**
   * Create test order for PIN testing
   */
  async createTestOrderForPinTesting() {
    const orderRef = doc(collection(this.db, 'orders'));
    const orderId = orderRef.id;
    const secretCode = Math.floor(100000 + Math.random() * 900000).toString();

    const order = {
      id: orderId,
      listingId: 'test_listing_pin',
      buyerId: this.auth.currentUser?.uid,
      sellerId: 'test_seller_pin',
      status: 'delivered',
      totalAmount: 50,
      secretCode,
      deliveredAt: serverTimestamp(),
      fundsReleased: false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      isTestOrder: true
    };

    await setDoc(orderRef, order);
    this.createdDocuments.push({ collection: 'orders', id: orderId });

    return order;
  }

  /**
   * Simulate time passage for auto-release testing
   */
  async simulateTimePassage(orderId, days) {
    const orderRef = doc(this.db, 'orders', orderId);
    const pastDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    await updateDoc(orderRef, {
      deliveredAt: pastDate,
      autoReleaseDate: pastDate,
      updatedAt: serverTimestamp()
    });

    console.log(`⏰ Simulated ${days} days passage for order: ${orderId}`);
  }

  /**
   * Trigger auto-release function
   */
  async triggerAutoRelease() {
    const autoReleaseFn = httpsCallable(this.functions, 'autoReleaseFunds');
    const response = await autoReleaseFn({});
    return response.data;
  }

  /**
   * Validate seller payouts
   */
  async validateSellerPayouts() {
    // Mock validation - in real implementation, check Stripe Connect payouts
    return {
      valid: true,
      totalPayouts: 2,
      totalAmount: 1050
    };
  }

  /**
   * Validate buyer order history
   */
  async validateBuyerOrderHistory() {
    // Mock validation - in real implementation, check order history completeness
    return {
      valid: true,
      totalOrders: 3,
      completedOrders: 2,
      refundedOrders: 1
    };
  }

  /**
   * Validate admin audit logs
   */
  async validateAdminAuditLogs() {
    // Mock validation - in real implementation, check audit log integrity
    return {
      valid: true,
      totalLogs: 15,
      pinLogsSecure: true,
      noSecurityViolations: true
    };
  }

  /**
   * Validate commission calculations
   */
  async validateCommissionCalculations() {
    const testData = TestDataFactory.generateTestData();

    // Test commission calculation for different price points
    const testCases = [
      { price: 3.00, category: 'Other', expectedCommission: 0.50 },
      { price: 25.00, category: 'Textbooks', expectedCommission: 2.00 },
      { price: 100.00, category: 'Electronics', expectedCommission: 10.00 }
    ];

    let allValid = true;

    for (const testCase of testCases) {
      const calculatedCommission = TestDataFactory.calculateExpectedCommission(
        testCase.price,
        testCase.category
      );

      if (Math.abs(calculatedCommission - testCase.expectedCommission) > 0.01) {
        allValid = false;
        console.error(`❌ Commission calculation failed for ${testCase.category} $${testCase.price}`);
      }
    }

    return {
      valid: allValid,
      testCases: testCases.length,
      commissionsCorrect: allValid
    };
  }
}
