"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWalletBalance = getWalletBalance;
exports.validateWalletUsage = validateWalletUsage;
exports.createWalletTransaction = createWalletTransaction;
exports.deductFromWallet = deductFromWallet;

exports.calculateFinalAmount = calculateFinalAmount;
exports.logWalletOperation = logWalletOperation;
const admin = __importStar(require("firebase-admin"));
/**
 * Gets the wallet balance for a user
 */
async function getWalletBalance(userId) {
    try {
        const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (!walletDoc.exists) {
            return 0;
        }
        const wallet = walletDoc.data();
        return wallet.balance || 0;
    }
    catch (error) {
        console.error('Error getting wallet balance:', error);
        throw new Error('Failed to fetch wallet balance');
    }
}
/**
 * Validates wallet balance usage
 */
function validateWalletUsage(requestedAmount, availableBalance, totalCost) {
    if (requestedAmount < 0) {
        return { isValid: false, error: 'Wallet amount cannot be negative' };
    }
    if (requestedAmount > availableBalance) {
        return {
            isValid: false,
            error: `Insufficient wallet balance. Available: $${availableBalance}, Requested: $${requestedAmount}`
        };
    }
    if (requestedAmount > totalCost) {
        return {
            isValid: false,
            error: `Wallet amount cannot exceed total cost. Total: $${totalCost}, Requested: $${requestedAmount}`
        };
    }
    return { isValid: true };
}
/**
 * Creates a wallet transaction record
 */
function createWalletTransaction(type, amount, description, source, orderId, grantedBy) {
    return {
        id: admin.firestore().collection('temp').doc().id,
        type,
        amount,
        description,
        source,
        orderId,
        grantedBy,
        createdAt: admin.firestore.Timestamp.now()
    };
}
/**
 * Deducts amount from wallet balance using Firestore transaction
 */
async function deductFromWallet(userId, amount, description, orderId, transaction) {
    const walletRef = admin.firestore().collection('wallets').doc(userId);
    const walletTransaction = createWalletTransaction('debit', amount, description, 'purchase_deduction', orderId);
    const updateData = {
        balance: admin.firestore.FieldValue.increment(-amount),
        history: admin.firestore.FieldValue.arrayUnion(walletTransaction),
        lastUpdated: admin.firestore.Timestamp.now()
    };
    if (transaction) {
        transaction.update(walletRef, updateData);
    }
    else {
        await walletRef.update(updateData);
    }
}

/**
 * Calculates the final amount to charge after wallet deduction
 */
function calculateFinalAmount(itemPrice, shippingCost, walletAmountUsed) {
    const totalBeforeWallet = itemPrice + shippingCost;
    const finalAmount = Math.max(0, totalBeforeWallet - walletAmountUsed);
    return { totalBeforeWallet, finalAmount };
}
/**
 * Logs wallet operation for debugging
 */
function logWalletOperation(operation, userId, amount, orderId) {
    console.log(`🔍 Wallet ${operation}: User: ${userId}, Amount: $${amount}${orderId ? `, Order: ${orderId}` : ''}`);
}
