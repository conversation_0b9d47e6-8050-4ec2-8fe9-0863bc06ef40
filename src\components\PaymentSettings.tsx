import React, { useState } from 'react';
import {
  CreditCard,
  Building2, // Replacing Bank with Building2
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Loader,
  ArrowRight
} from 'lucide-react';
import { useStripeConnect } from '../hooks/useStripeConnect';
import { useAuth } from '../hooks/useAuth';

const PaymentSettings: React.FC = () => {
  const { userProfile } = useAuth();
  const {
    accountStatus,
    isLoading: isLoadingAccount,
    error: stripeError,
    createAccount,
    getOnboardingLink,
    refreshAccountStatus
  } = useStripeConnect({ autoLoad: true });

  const [error, setError] = useState<string | null>(null);
  const [isCreatingAccount, setIsCreatingAccount] = useState(false);
  const [accountLinkUrl, setAccountLinkUrl] = useState<string | null>(null);
  const [isGettingOnboardingLink, setIsGettingOnboardingLink] = useState(false);

  // Use error from useStripeConnect hook, but allow local error override
  const displayError = error || stripeError;

  // Handle creating a Connect account
  const handleCreateConnectAccount = async (accountType: 'student' | 'merchant') => {
    if (!userProfile) return;
    
    setIsCreatingAccount(true);
    setError(null);
    
    try {
      const { onboardingUrl } = await createAccount(accountType);
      setAccountLinkUrl(onboardingUrl);
    } catch (error: unknown) {
      console.error('Error creating Connect account:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create payment account';

      if (errorMessage.includes('already has a fully onboarded Connect account')) {
        setError('Your payment account is already set up! Please refresh the page.');
        // Refresh the connect account data
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsCreatingAccount(false);
    }
  };

  const handleCompleteSetup = async () => {
    if (!accountStatus) return;

    setIsGettingOnboardingLink(true);
    setError(null);

    try {
      const baseUrl = window.location.origin;

      // First try the Firebase function
      try {
        const { onboardingUrl } = await getOnboardingLink(
          `${baseUrl}/profile?stripe_refresh=true`,
          `${baseUrl}/profile?stripe_success=true`
        );

        console.log('Got onboarding URL from Firebase function:', onboardingUrl);

        // Check if it's a valid Stripe URL
        if (onboardingUrl && (onboardingUrl.includes('connect.stripe.com') || onboardingUrl.includes('stripe.com'))) {
          window.open(onboardingUrl, '_blank');
          return;
        }
      } catch (functionError) {
        console.warn('Firebase function failed, trying fallback:', functionError);
      }

      // Fallback: Use direct Stripe Connect URL if we have an account ID
      if (accountStatus.accountId) {
        const fallbackUrl = `https://connect.stripe.com/setup/s/${accountStatus.accountId}`;
        console.log('Using fallback Stripe URL:', fallbackUrl);
        window.open(fallbackUrl, '_blank');
      } else {
        throw new Error('No account ID available for Stripe setup');
      }

    } catch (error: unknown) {
      console.error('Error getting onboarding link:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to get setup link';
      setError(`Unable to open Stripe setup: ${errorMessage}. Please try refreshing the page.`);
    } finally {
      setIsGettingOnboardingLink(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Account Status */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Payment Account</h3>
        
        {isLoadingAccount ? (
          <div className="flex items-center justify-center py-8">
            <Loader className="w-6 h-6 text-primary-600 dark:text-primary-400 animate-spin" />
          </div>
        ) : displayError ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
              <div>
                <p className="font-medium text-red-800 dark:text-red-200">Error</p>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">{displayError}</p>
                <button
                  onClick={refreshAccountStatus}
                  className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        ) : accountStatus ? (
          <div>
            <div className={`p-4 rounded-xl mb-6 ${
              accountStatus.isOnboarded
                ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                : 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
            }`}>
              <div className="flex items-start space-x-3">
                {accountStatus.isOnboarded ? (
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                )}
                <div>
                  <p className={`font-medium ${
                    accountStatus.isOnboarded
                      ? 'text-green-800 dark:text-green-200'
                      : 'text-yellow-800 dark:text-yellow-200'
                  }`}>
                    {accountStatus.isOnboarded ? 'Account Active' : 'Account Setup Incomplete'}
                  </p>
                  <p className={`text-sm mt-1 ${
                    accountStatus.isOnboarded
                      ? 'text-green-700 dark:text-green-300'
                      : 'text-yellow-700 dark:text-yellow-300'
                  }`}>
                    {accountStatus.isOnboarded
                      ? 'Your payment account is set up and ready to receive payments.'
                      : 'Please complete your account setup to receive payments.'}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-xl">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Stripe Account</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Connected to Stripe ({accountStatus.accountId.substring(0, 8)}...)
                    </p>
                  </div>
                </div>

                {!accountStatus.isOnboarded && (
                  <button
                    onClick={handleCompleteSetup}
                    disabled={isGettingOnboardingLink}
                    className="flex items-center space-x-1 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isGettingOnboardingLink ? (
                      <>
                        <Loader className="w-4 h-4 animate-spin" />
                        <span className="text-sm font-medium">Getting Setup Link...</span>
                      </>
                    ) : (
                      <>
                        <span className="text-sm font-medium">Complete Setup</span>
                        <ExternalLink className="w-4 h-4" />
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>

            {accountStatus.isOnboarded && (
              <div className="mt-6">
                <button
                  onClick={() => window.open(accountStatus.dashboardUrl || `https://dashboard.stripe.com/connect/accounts/${accountStatus.accountId}`, '_blank')}
                  className="w-full border border-primary-600 text-primary-600 dark:text-primary-400 py-3 rounded-xl font-semibold hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all flex items-center justify-center space-x-2"
                >
                  <ExternalLink className="w-5 h-5" />
                  <span>View Stripe Dashboard</span>
                </button>
              </div>
            )}
          </div>
        ) : accountLinkUrl ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <ExternalLink className="w-8 h-8 text-primary-600 dark:text-primary-400" />
            </div>
            <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Complete Your Account Setup
            </h4>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              You'll be redirected to Stripe to complete your account setup. This will allow you to receive payments for your listings.
            </p>
            <a 
              href={accountLinkUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-primary-600 hover:bg-primary-700 text-white py-3 px-6 rounded-xl font-semibold transition-colors"
            >
              Continue to Stripe
            </a>
          </div>
        ) : (
          <div>
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-800 dark:text-blue-200">No Payment Account</p>
                  <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                    You need to set up a payment account to receive payments for your listings.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <button
                onClick={() => handleCreateConnectAccount('student')}
                disabled={isCreatingAccount}
                className="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                    <CreditCard className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-gray-900 dark:text-white">Student Seller Account</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      For individual students selling personal items
                    </p>
                  </div>
                </div>
                {isCreatingAccount ? (
                  <Loader className="w-5 h-5 text-primary-600 dark:text-primary-400 animate-spin" />
                ) : (
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                )}
              </button>
              
              <button
                onClick={() => handleCreateConnectAccount('merchant')}
                disabled={isCreatingAccount}
                className="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                    <Building2 className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-gray-900 dark:text-white">Merchant Partner Account</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      For campus businesses and student organizations
                    </p>
                  </div>
                </div>
                {isCreatingAccount ? (
                  <Loader className="w-5 h-5 text-primary-600 dark:text-primary-400 animate-spin" />
                ) : (
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* Commission Rates */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Commission Rates</h3>
        <div className="space-y-4">
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-xl">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium text-gray-900 dark:text-white">Textbooks & Course Materials</p>
              <p className="text-lg font-semibold text-primary-600 dark:text-primary-400">8%</p>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Lower commission rate to help students save on educational materials
            </p>
          </div>
          
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-xl">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium text-gray-900 dark:text-white">All Other Categories</p>
              <p className="text-lg font-semibold text-primary-600 dark:text-primary-400">10%</p>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Standard commission rate for electronics, clothing, furniture, etc.
            </p>
          </div>
        </div>
        
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
          Commission rates include all Stripe processing fees. You receive 92% or 90% of the sale price, depending on the category.
        </p>
      </div>
      
      {/* Payment FAQ */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Payment FAQ</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">When will I receive my money?</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Funds are held in escrow until the buyer confirms receipt of the item by entering the secret code. If the buyer doesn't enter the code, funds are automatically released after 3 days.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">How do I track my earnings?</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              You can view all your transactions and payouts in your Stripe dashboard. We also provide a summary in your Hive Campus account.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">What information do I need to provide?</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Stripe requires basic personal information, including your name, address, date of birth, and the last 4 digits of your SSN to verify your identity and comply with financial regulations.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">Is my information secure?</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Yes, all your payment and personal information is securely handled by Stripe, a PCI-compliant payment processor. Hive Campus does not store your banking details.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSettings;