﻿/**
 * Security utilities for Hive Campus
 * Handles HTTPS enforcement, input validation, and other security measures
 */

/**
 * Enforce HTTPS redirect
 */
export const enforceHTTPS = () => {
  // Only enforce HTTPS in production
  if (window.location.protocol === 'http:' && window.location.hostname !== 'localhost') {
    const httpsUrl = `https://${window.location.host}${window.location.pathname}${window.location.search}${window.location.hash}`;
    window.location.replace(httpsUrl);
  }
};

/**
 * Check if current connection is secure
 */
export const isSecureConnection = (): boolean => {
  return window.location.protocol === 'https:' || window.location.hostname === 'localhost';
};

/**
 * Security headers validation
 */
export const validateSecurityHeaders = async (): Promise<{
  hsts: boolean;
  csp: boolean;
  xframe: boolean;
  xcto: boolean;
  xxss: boolean;
}> => {
  try {
    const response = await fetch(window.location.origin, { method: 'HEAD' });
    
    return {
      hsts: response.headers.has('strict-transport-security'),
      csp: response.headers.has('content-security-policy'),
      xframe: response.headers.has('x-frame-options'),
      xcto: response.headers.has('x-content-type-options'),
      xxss: response.headers.has('x-xss-protection'),
    };
  } catch (error) {
    console.warn('Could not validate security headers:', error);
    return {
      hsts: false,
      csp: false,
      xframe: false,
      xcto: false,
      xxss: false,
    };
  }
};

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize HTML input to prevent XSS
   */
  static sanitizeHTML(input: string): string {
    const div = document.createElement('div');
    div.textContent = input;
    return div.innerHTML;
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  /**
   * Validate educational email (.edu domain)
   */
  static isEducationalEmail(email: string): boolean {
    return this.isValidEmail(email) && email.toLowerCase().endsWith('.edu');
  }

  /**
   * Sanitize user input for safe storage
   */
  static sanitizeUserInput(input: string): string {
    return input
      .trim()
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .slice(0, 1000); // Limit length
  }

  /**
   * Validate URL to prevent open redirects
   */
  static isValidRedirectURL(url: string): boolean {
    try {
      const urlObj = new URL(url);

      // Get allowed hosts from environment or use secure defaults
      const projectId = import.meta.env.VITE_FIREBASE_PROJECT_ID;
      const allowedHosts = [
        // Production domains
        'hivecampus.app',
        'www.hivecampus.app',
        // Firebase hosting domains
        ...(projectId ? [`${projectId}.web.app`, `${projectId}.firebaseapp.com`] : []),
        // Development
        'localhost'
      ].filter(Boolean);

      return allowedHosts.some(host =>
        urlObj.hostname === host || urlObj.hostname.endsWith(`.${host}`)
      );
    } catch {
      return false;
    }
  }

  /**
   * Remove potentially dangerous characters from file names
   */
  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace special chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .slice(0, 100); // Limit length
  }
}

/**
 * Content Security Policy utilities
 */
export class CSPManager {
  /**
   * Generate nonce for inline scripts/styles
   */
  static generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  /**
   * Add nonce to script element
   */
  static addNonceToScript(script: HTMLScriptElement, nonce: string): void {
    script.setAttribute('nonce', nonce);
  }

  /**
   * Validate external resource URL against CSP
   */
  static isAllowedExternalResource(url: string, type: 'script' | 'style' | 'img' | 'font'): boolean {
    const allowedDomains: Record<string, string[]> = {
      script: [
        'www.gstatic.com',
        'www.googleapis.com',
        'js.stripe.com',
        'api.reeflex.ai',
        'browser.sentry-cdn.com'
      ],
      style: [
        'fonts.googleapis.com'
      ],
      img: [
        'avatars.githubusercontent.com',
        'lh3.googleusercontent.com'
      ],
      font: [
        'fonts.gstatic.com'
      ]
    };

    try {
      const urlObj = new URL(url);
      return allowedDomains[type]?.some(domain => 
        urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
      ) || false;
    } catch {
      return false;
    }
  }
}

/**
 * Authentication security utilities
 */
export class AuthSecurity {
  /**
   * Generate secure random password
   */
  static generateSecurePassword(length: number = 16): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    
    return Array.from(array, byte => charset[byte % charset.length]).join('');
  }

  /**
   * Validate password strength
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    if (password.length < 8) {
      feedback.push('Password must be at least 8 characters long');
    } else {
      score += 1;
    }

    if (!/[a-z]/.test(password)) {
      feedback.push('Password must contain lowercase letters');
    } else {
      score += 1;
    }

    if (!/[A-Z]/.test(password)) {
      feedback.push('Password must contain uppercase letters');
    } else {
      score += 1;
    }

    if (!/[0-9]/.test(password)) {
      feedback.push('Password must contain numbers');
    } else {
      score += 1;
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
      feedback.push('Password must contain special characters');
    } else {
      score += 1;
    }

    return {
      isValid: feedback.length === 0,
      score,
      feedback
    };
  }

  /**
   * Generate CSRF token
   */
  static generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  /**
   * Validate session timing to prevent session fixation
   */
  static validateSessionTiming(sessionStart: number, maxSessionTime: number = 8 * 60 * 60 * 1000): boolean {
    const now = Date.now();
    return (now - sessionStart) < maxSessionTime;
  }
}

/**
 * Rate limiting utilities for client-side
 */
export class ClientRateLimit {
  private static requests: Map<string, number[]> = new Map();

  /**
   * Check if action is rate limited
   */
  static isRateLimited(
    action: string, 
    maxRequests: number = 10, 
    windowMs: number = 60000
  ): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get existing requests for this action
    const actionRequests = this.requests.get(action) || [];
    
    // Filter out old requests
    const recentRequests = actionRequests.filter(time => time > windowStart);
    
    // Check if limit exceeded
    if (recentRequests.length >= maxRequests) {
      return true;
    }
    
    // Add current request
    recentRequests.push(now);
    this.requests.set(action, recentRequests);
    
    return false;
  }

  /**
   * Clear rate limit data for an action
   */
  static clearRateLimit(action: string): void {
    this.requests.delete(action);
  }
}

/**
 * Secure local storage utilities
 */
export class SecureStorage {
  /**
   * Encrypt data before storing (simple XOR for demonstration)
   */
  private static encrypt(data: string, key: string): string {
    let result = '';
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(result);
  }

  /**
   * Decrypt data after retrieving
   */
  private static decrypt(encryptedData: string, key: string): string {
    const data = atob(encryptedData);
    let result = '';
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return result;
  }

  /**
   * Store sensitive data securely
   */
  static setSecureItem(key: string, value: string): void {
    const encryptionKey = this.getEncryptionKey();
    const encrypted = this.encrypt(value, encryptionKey);
    localStorage.setItem(key, encrypted);
  }

  /**
   * Retrieve sensitive data securely
   */
  static getSecureItem(key: string): string | null {
    const encrypted = localStorage.getItem(key);
    if (!encrypted) return null;

    try {
      const encryptionKey = this.getEncryptionKey();
      return this.decrypt(encrypted, encryptionKey);
    } catch {
      // If decryption fails, remove the corrupted data
      localStorage.removeItem(key);
      return null;
    }
  }

  /**
   * Generate or retrieve encryption key
   */
  private static getEncryptionKey(): string {
    let key = sessionStorage.getItem('_sk');
    if (!key) {
      key = AuthSecurity.generateSecurePassword(32);
      sessionStorage.setItem('_sk', key);
    }
    return key;
  }

  /**
   * Clear all secure storage
   */
  static clearSecureStorage(): void {
    sessionStorage.removeItem('_sk');
    // Clear known sensitive keys
    const sensitiveKeys = ['user-preferences', 'temp-data', 'form-data'];
    sensitiveKeys.forEach(key => localStorage.removeItem(key));
  }
}

/**
 * Initialize security measures
 */
export const initializeSecurity = () => {
  // Enforce HTTPS
  enforceHTTPS();

  // Validate security headers in development
  if (import.meta.env.MODE === 'development') {
    validateSecurityHeaders().then(headers => {
      console.log('Security Headers Status:', headers);
    });
  }

  // Set up CSP violation reporting
  document.addEventListener('securitypolicyviolation', (e) => {
    console.warn('CSP Violation:', {
      violatedDirective: e.violatedDirective,
      blockedURI: e.blockedURI,
      documentURI: e.documentURI,
      lineNumber: e.lineNumber,
    });
  });

  // Clear sensitive data when page becomes hidden (modern alternative to beforeunload)
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      SecureStorage.clearSecureStorage();
    }
  });

  console.log('Security measures initialized');
};
