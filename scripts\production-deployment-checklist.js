#!/usr/bin/env node

/**
 * 🚀 PRODUCTION DEPLOYMENT VALIDATION SCRIPT
 * Hive Campus Production Deployment Checklist
 * 
 * Validates all systems before production deployment
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const DEPLOYMENT_TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-');
const REPORT_FILE = `production-deployment-report-${DEPLOYMENT_TIMESTAMP}.json`;

class ProductionValidator {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      deploymentReady: false,
      validationResults: {},
      criticalIssues: [],
      warnings: [],
      recommendations: []
    };
  }

  async validateProductionDeployment() {
    console.log('🚀 Starting Production Deployment Validation...\n');

    try {
      // 1. Environment Configuration Check
      await this.validateEnvironmentConfig();
      
      // 2. Firebase Configuration Check
      await this.validateFirebaseConfig();
      
      // 3. Security Configuration Check
      await this.validateSecurityConfig();
      
      // 4. API Integration Check
      await this.validateAPIIntegrations();
      
      // 5. Database Schema Validation
      await this.validateDatabaseSchema();
      
      // 6. Build and Asset Validation
      await this.validateBuildAssets();
      
      // 7. Performance Validation
      await this.validatePerformance();
      
      // 8. Monitoring and Logging Check
      await this.validateMonitoring();
      
      // 9. Final Deployment Readiness Assessment
      await this.assessDeploymentReadiness();
      
    } catch (error) {
      console.error('❌ Production validation failed:', error);
      this.results.criticalIssues.push({
        category: 'VALIDATION_FAILURE',
        description: error.message,
        severity: 'CRITICAL'
      });
    }
  }

  async validateEnvironmentConfig() {
    console.log('⚙️ Validating Environment Configuration...');
    
    const requiredEnvVars = {
      // Firebase Configuration
      'VITE_FIREBASE_API_KEY': { required: true, production: true },
      'VITE_FIREBASE_PROJECT_ID': { required: true, production: true, pattern: /^hive-campus/ },
      'VITE_FIREBASE_AUTH_DOMAIN': { required: true, production: true },
      'VITE_FIREBASE_STORAGE_BUCKET': { required: true, production: true },
      'VITE_FIREBASE_MESSAGING_SENDER_ID': { required: true, production: true },
      'VITE_FIREBASE_APP_ID': { required: true, production: true },
      
      // Stripe Configuration
      'VITE_STRIPE_PUBLISHABLE_KEY': { required: true, production: true, pattern: /^pk_live_/ },
      
      // Monitoring Configuration
      'VITE_SENTRY_DSN': { required: true, production: true },
      'VITE_REEFLEX_API_KEY': { required: true, production: true },
      
      // Security Configuration
      'HIVE_PIN_SALT': { required: true, production: false, minLength: 32 },
      'STRIPE_WEBHOOK_SECRET': { required: true, production: false, minLength: 32 },
      'JWT_SECRET': { required: true, production: false, minLength: 32 }
    };

    const issues = [];
    const warnings = [];

    Object.entries(requiredEnvVars).forEach(([varName, config]) => {
      const value = process.env[varName];
      
      if (!value) {
        issues.push({
          category: 'MISSING_ENV_VAR',
          description: `Missing required environment variable: ${varName}`,
          severity: 'CRITICAL'
        });
        return;
      }

      // Check production patterns
      if (config.pattern && !config.pattern.test(value)) {
        if (config.production) {
          issues.push({
            category: 'INVALID_PRODUCTION_CONFIG',
            description: `${varName} does not match production pattern`,
            severity: 'CRITICAL'
          });
        } else {
          warnings.push({
            category: 'CONFIG_WARNING',
            description: `${varName} may not be production-ready`,
            severity: 'WARNING'
          });
        }
      }

      // Check minimum length for security variables
      if (config.minLength && value.length < config.minLength) {
        issues.push({
          category: 'WEAK_SECURITY_CONFIG',
          description: `${varName} is too short (minimum ${config.minLength} characters)`,
          severity: 'HIGH'
        });
      }
    });

    this.results.validationResults.environmentConfig = {
      status: issues.length === 0 ? 'PASSED' : 'FAILED',
      issues: issues.length,
      warnings: warnings.length,
      details: { issues, warnings }
    };

    this.results.criticalIssues.push(...issues);
    this.results.warnings.push(...warnings);

    console.log(`   ✅ Environment config: ${issues.length} issues, ${warnings.length} warnings`);
  }

  async validateFirebaseConfig() {
    console.log('🔥 Validating Firebase Configuration...');
    
    const issues = [];
    
    try {
      // Check firebase.json configuration
      const firebaseConfigPath = path.join(process.cwd(), 'firebase.json');
      
      if (!fs.existsSync(firebaseConfigPath)) {
        issues.push({
          category: 'MISSING_CONFIG',
          description: 'firebase.json configuration file not found',
          severity: 'CRITICAL'
        });
      } else {
        const firebaseConfig = JSON.parse(fs.readFileSync(firebaseConfigPath, 'utf8'));
        
        // Validate required services
        const requiredServices = ['hosting', 'functions', 'firestore', 'storage'];
        const configuredServices = Object.keys(firebaseConfig);
        
        const missingServices = requiredServices.filter(service => 
          !configuredServices.includes(service)
        );
        
        if (missingServices.length > 0) {
          issues.push({
            category: 'INCOMPLETE_FIREBASE_CONFIG',
            description: `Missing Firebase services: ${missingServices.join(', ')}`,
            severity: 'HIGH'
          });
        }

        // Check hosting configuration
        if (firebaseConfig.hosting) {
          const hosting = firebaseConfig.hosting;
          
          if (!hosting.public || hosting.public !== 'dist') {
            issues.push({
              category: 'INVALID_HOSTING_CONFIG',
              description: 'Firebase hosting should serve from dist directory',
              severity: 'MEDIUM'
            });
          }

          if (!hosting.rewrites || !hosting.rewrites.some(r => r.source === '**' && r.destination === '/index.html')) {
            issues.push({
              category: 'MISSING_SPA_REWRITE',
              description: 'Missing SPA rewrite rule in Firebase hosting',
              severity: 'MEDIUM'
            });
          }
        }
      }

      // Check Firestore rules
      const rulesPath = path.join(process.cwd(), 'firestore.rules');
      
      if (!fs.existsSync(rulesPath)) {
        issues.push({
          category: 'MISSING_FIRESTORE_RULES',
          description: 'Firestore security rules file not found',
          severity: 'CRITICAL'
        });
      } else {
        const rulesContent = fs.readFileSync(rulesPath, 'utf8');
        
        // Check for critical security patterns
        const securityPatterns = [
          { pattern: /isAuthenticated\(\)/, name: 'Authentication checks' },
          { pattern: /allow read, write: if false/, name: 'System-only collections' }
        ];
        
        const missingPatterns = securityPatterns.filter(p => !p.pattern.test(rulesContent));
        
        if (missingPatterns.length > 0) {
          issues.push({
            category: 'WEAK_FIRESTORE_RULES',
            description: `Missing security patterns: ${missingPatterns.map(p => p.name).join(', ')}`,
            severity: 'HIGH'
          });
        }
      }

    } catch (error) {
      issues.push({
        category: 'FIREBASE_CONFIG_ERROR',
        description: `Firebase configuration validation failed: ${error.message}`,
        severity: 'CRITICAL'
      });
    }

    this.results.validationResults.firebaseConfig = {
      status: issues.length === 0 ? 'PASSED' : 'FAILED',
      issues: issues.length,
      details: issues
    };

    this.results.criticalIssues.push(...issues);

    console.log(`   ✅ Firebase config: ${issues.length} issues found`);
  }

  async validateSecurityConfig() {
    console.log('🔐 Validating Security Configuration...');
    
    const issues = [];
    const warnings = [];

    try {
      // Check for security headers in hosting configuration
      const firebaseConfigPath = path.join(process.cwd(), 'firebase.json');
      
      if (fs.existsSync(firebaseConfigPath)) {
        const firebaseConfig = JSON.parse(fs.readFileSync(firebaseConfigPath, 'utf8'));
        
        if (firebaseConfig.hosting && firebaseConfig.hosting.headers) {
          const headers = firebaseConfig.hosting.headers;
          
          const requiredSecurityHeaders = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
            'Strict-Transport-Security'
          ];
          
          const configuredHeaders = headers.flatMap(h => 
            h.headers ? h.headers.map(header => header.key) : []
          );
          
          const missingHeaders = requiredSecurityHeaders.filter(header => 
            !configuredHeaders.includes(header)
          );
          
          if (missingHeaders.length > 0) {
            warnings.push({
              category: 'MISSING_SECURITY_HEADERS',
              description: `Missing security headers: ${missingHeaders.join(', ')}`,
              severity: 'MEDIUM'
            });
          }
        } else {
          warnings.push({
            category: 'NO_SECURITY_HEADERS',
            description: 'No security headers configured in Firebase hosting',
            severity: 'MEDIUM'
          });
        }
      }

      // Check for HTTPS enforcement
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // Check for security-related dependencies
        const securityDeps = [
          '@sentry/react',
          'firebase'
        ];
        
        const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
        const missingSecurityDeps = securityDeps.filter(dep => !allDeps[dep]);
        
        if (missingSecurityDeps.length > 0) {
          issues.push({
            category: 'MISSING_SECURITY_DEPENDENCIES',
            description: `Missing security dependencies: ${missingSecurityDeps.join(', ')}`,
            severity: 'HIGH'
          });
        }
      }

    } catch (error) {
      issues.push({
        category: 'SECURITY_CONFIG_ERROR',
        description: `Security configuration validation failed: ${error.message}`,
        severity: 'HIGH'
      });
    }

    this.results.validationResults.securityConfig = {
      status: issues.length === 0 ? 'PASSED' : 'FAILED',
      issues: issues.length,
      warnings: warnings.length,
      details: { issues, warnings }
    };

    this.results.criticalIssues.push(...issues);
    this.results.warnings.push(...warnings);

    console.log(`   ✅ Security config: ${issues.length} issues, ${warnings.length} warnings`);
  }

  async validateAPIIntegrations() {
    console.log('🔗 Validating API Integrations...');
    
    const integrations = [
      { name: 'Stripe', key: 'VITE_STRIPE_PUBLISHABLE_KEY', pattern: /^pk_live_/ },
      { name: 'Sentry', key: 'VITE_SENTRY_DSN', pattern: /^https:\/\/.*@sentry\.io/ },
      { name: 'ReeFlex', key: 'VITE_REEFLEX_API_KEY', minLength: 20 }
    ];

    const issues = [];
    const warnings = [];

    integrations.forEach(integration => {
      const value = process.env[integration.key];
      
      if (!value) {
        issues.push({
          category: 'MISSING_API_KEY',
          description: `Missing ${integration.name} API key`,
          severity: 'HIGH'
        });
        return;
      }

      if (integration.pattern && !integration.pattern.test(value)) {
        issues.push({
          category: 'INVALID_API_KEY',
          description: `${integration.name} API key format is invalid`,
          severity: 'HIGH'
        });
      }

      if (integration.minLength && value.length < integration.minLength) {
        warnings.push({
          category: 'SHORT_API_KEY',
          description: `${integration.name} API key may be too short`,
          severity: 'MEDIUM'
        });
      }
    });

    this.results.validationResults.apiIntegrations = {
      status: issues.length === 0 ? 'PASSED' : 'FAILED',
      issues: issues.length,
      warnings: warnings.length,
      details: { issues, warnings }
    };

    this.results.criticalIssues.push(...issues);
    this.results.warnings.push(...warnings);

    console.log(`   ✅ API integrations: ${issues.length} issues, ${warnings.length} warnings`);
  }

  async validateDatabaseSchema() {
    console.log('🗄️ Validating Database Schema...');
    
    const issues = [];
    
    try {
      // Check for required Firestore indexes
      const indexesPath = path.join(process.cwd(), 'firestore.indexes.json');
      
      if (!fs.existsSync(indexesPath)) {
        issues.push({
          category: 'MISSING_INDEXES',
          description: 'Firestore indexes configuration not found',
          severity: 'HIGH'
        });
      } else {
        const indexesConfig = JSON.parse(fs.readFileSync(indexesPath, 'utf8'));
        
        // Check for critical indexes
        const requiredIndexes = [
          { collection: 'listings', fields: ['sellerId', 'status'] },
          { collection: 'orders', fields: ['buyerId', 'status'] },
          { collection: 'chats', fields: ['participants', 'updatedAt'] }
        ];
        
        const configuredIndexes = indexesConfig.indexes || [];
        
        requiredIndexes.forEach(requiredIndex => {
          const hasIndex = configuredIndexes.some(index => 
            index.collectionGroup === requiredIndex.collection &&
            requiredIndex.fields.every(field => 
              index.fields.some(f => f.fieldPath === field)
            )
          );
          
          if (!hasIndex) {
            issues.push({
              category: 'MISSING_REQUIRED_INDEX',
              description: `Missing index for ${requiredIndex.collection} on ${requiredIndex.fields.join(', ')}`,
              severity: 'MEDIUM'
            });
          }
        });
      }

    } catch (error) {
      issues.push({
        category: 'DATABASE_SCHEMA_ERROR',
        description: `Database schema validation failed: ${error.message}`,
        severity: 'HIGH'
      });
    }

    this.results.validationResults.databaseSchema = {
      status: issues.length === 0 ? 'PASSED' : 'FAILED',
      issues: issues.length,
      details: issues
    };

    this.results.criticalIssues.push(...issues);

    console.log(`   ✅ Database schema: ${issues.length} issues found`);
  }

  async validateBuildAssets() {
    console.log('📦 Validating Build Assets...');
    
    const issues = [];
    const warnings = [];

    try {
      // Check if build directory exists
      const buildDir = path.join(process.cwd(), 'dist');
      
      if (!fs.existsSync(buildDir)) {
        issues.push({
          category: 'MISSING_BUILD',
          description: 'Build directory (dist) not found. Run npm run build first.',
          severity: 'CRITICAL'
        });
      } else {
        // Check for required build files
        const requiredFiles = [
          'index.html',
          'manifest.json',
          'firebase-messaging-sw.js'
        ];
        
        requiredFiles.forEach(file => {
          const filePath = path.join(buildDir, file);
          if (!fs.existsSync(filePath)) {
            issues.push({
              category: 'MISSING_BUILD_FILE',
              description: `Missing required build file: ${file}`,
              severity: 'HIGH'
            });
          }
        });

        // Check asset sizes
        const assetsDir = path.join(buildDir, 'assets');
        
        if (fs.existsSync(assetsDir)) {
          const assetFiles = fs.readdirSync(assetsDir);
          const largeAssets = assetFiles.filter(file => {
            const filePath = path.join(assetsDir, file);
            const stats = fs.statSync(filePath);
            return stats.size > 1024 * 1024; // 1MB
          });
          
          if (largeAssets.length > 0) {
            warnings.push({
              category: 'LARGE_ASSETS',
              description: `Large assets detected: ${largeAssets.join(', ')}`,
              severity: 'MEDIUM'
            });
          }
        }
      }

    } catch (error) {
      issues.push({
        category: 'BUILD_VALIDATION_ERROR',
        description: `Build validation failed: ${error.message}`,
        severity: 'HIGH'
      });
    }

    this.results.validationResults.buildAssets = {
      status: issues.length === 0 ? 'PASSED' : 'FAILED',
      issues: issues.length,
      warnings: warnings.length,
      details: { issues, warnings }
    };

    this.results.criticalIssues.push(...issues);
    this.results.warnings.push(...warnings);

    console.log(`   ✅ Build assets: ${issues.length} issues, ${warnings.length} warnings`);
  }

  async validatePerformance() {
    console.log('⚡ Validating Performance Configuration...');
    
    const warnings = [];

    try {
      // Check for performance optimizations in build
      const viteConfigPath = path.join(process.cwd(), 'vite.config.ts');
      
      if (fs.existsSync(viteConfigPath)) {
        const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
        
        // Check for build optimizations
        if (!viteConfig.includes('minify')) {
          warnings.push({
            category: 'MISSING_MINIFICATION',
            description: 'Minification not explicitly configured in Vite',
            severity: 'LOW'
          });
        }

        if (!viteConfig.includes('rollupOptions')) {
          warnings.push({
            category: 'MISSING_ROLLUP_OPTIMIZATION',
            description: 'Rollup optimizations not configured',
            severity: 'LOW'
          });
        }
      }

      // Check for PWA configuration
      const manifestPath = path.join(process.cwd(), 'public', 'manifest.json');
      
      if (fs.existsSync(manifestPath)) {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        
        if (!manifest.icons || manifest.icons.length === 0) {
          warnings.push({
            category: 'MISSING_PWA_ICONS',
            description: 'PWA manifest missing icons',
            severity: 'LOW'
          });
        }
      }

    } catch (error) {
      warnings.push({
        category: 'PERFORMANCE_VALIDATION_ERROR',
        description: `Performance validation failed: ${error.message}`,
        severity: 'LOW'
      });
    }

    this.results.validationResults.performance = {
      status: 'PASSED',
      warnings: warnings.length,
      details: warnings
    };

    this.results.warnings.push(...warnings);

    console.log(`   ✅ Performance: ${warnings.length} optimization opportunities`);
  }

  async validateMonitoring() {
    console.log('📊 Validating Monitoring and Logging...');
    
    const issues = [];
    const warnings = [];

    try {
      // Check Sentry configuration
      const sentryDSN = process.env.VITE_SENTRY_DSN;
      
      if (!sentryDSN) {
        issues.push({
          category: 'MISSING_ERROR_TRACKING',
          description: 'Sentry DSN not configured for error tracking',
          severity: 'HIGH'
        });
      }

      // Check for monitoring configuration files
      const monitoringFiles = [
        'monitoring.config.js',
        'sentry.config.js'
      ];

      monitoringFiles.forEach(file => {
        const filePath = path.join(process.cwd(), file);
        if (!fs.existsSync(filePath)) {
          warnings.push({
            category: 'MISSING_MONITORING_CONFIG',
            description: `Missing monitoring configuration: ${file}`,
            severity: 'MEDIUM'
          });
        }
      });

    } catch (error) {
      issues.push({
        category: 'MONITORING_VALIDATION_ERROR',
        description: `Monitoring validation failed: ${error.message}`,
        severity: 'MEDIUM'
      });
    }

    this.results.validationResults.monitoring = {
      status: issues.length === 0 ? 'PASSED' : 'FAILED',
      issues: issues.length,
      warnings: warnings.length,
      details: { issues, warnings }
    };

    this.results.criticalIssues.push(...issues);
    this.results.warnings.push(...warnings);

    console.log(`   ✅ Monitoring: ${issues.length} issues, ${warnings.length} warnings`);
  }

  async assessDeploymentReadiness() {
    console.log('\n🎯 Assessing Deployment Readiness...');
    
    const criticalIssues = this.results.criticalIssues.filter(issue => 
      issue.severity === 'CRITICAL'
    );
    
    const highIssues = this.results.criticalIssues.filter(issue => 
      issue.severity === 'HIGH'
    );

    // Determine deployment readiness
    if (criticalIssues.length === 0 && highIssues.length <= 2) {
      this.results.deploymentReady = true;
      this.results.recommendations.push('✅ System is ready for production deployment');
    } else {
      this.results.deploymentReady = false;
      
      if (criticalIssues.length > 0) {
        this.results.recommendations.push('🚨 CRITICAL: Fix all critical issues before deployment');
      }
      
      if (highIssues.length > 2) {
        this.results.recommendations.push('⚠️ HIGH: Address high-severity issues for optimal security');
      }
    }

    // Generate specific recommendations
    if (this.results.warnings.length > 0) {
      this.results.recommendations.push(`💡 Consider addressing ${this.results.warnings.length} warnings for improved performance`);
    }

    // Save final report
    fs.writeFileSync(REPORT_FILE, JSON.stringify(this.results, null, 2));

    // Display summary
    this.displayDeploymentSummary();
  }

  displayDeploymentSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('🚀 PRODUCTION DEPLOYMENT VALIDATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`📅 Validation Date: ${this.results.timestamp}`);
    console.log(`🎯 Deployment Ready: ${this.results.deploymentReady ? 'YES' : 'NO'}`);
    console.log(`🚨 Critical Issues: ${this.results.criticalIssues.filter(i => i.severity === 'CRITICAL').length}`);
    console.log(`⚠️ High Issues: ${this.results.criticalIssues.filter(i => i.severity === 'HIGH').length}`);
    console.log(`💡 Warnings: ${this.results.warnings.length}`);
    
    if (this.results.criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES TO FIX:');
      this.results.criticalIssues
        .filter(issue => issue.severity === 'CRITICAL')
        .forEach(issue => {
          console.log(`   • ${issue.description}`);
        });
    }
    
    console.log('\n📋 RECOMMENDATIONS:');
    this.results.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });
    
    console.log(`\n📄 Full report saved to: ${REPORT_FILE}`);
    console.log('='.repeat(60));
  }
}

// Run the validation
const validator = new ProductionValidator();
validator.validateProductionDeployment().catch(console.error);
