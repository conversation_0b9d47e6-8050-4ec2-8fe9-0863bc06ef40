import React, { useState } from 'react';
import PaymentDetailsCard from './PaymentDetailsCard';

const PaymentDetailsTest: React.FC = () => {
  const [testOrderId, setTestOrderId] = useState('');

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Payment Details Test</h1>
      
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          Enter Order ID to test:
        </label>
        <input
          type="text"
          value={testOrderId}
          onChange={(e) => setTestOrderId(e.target.value)}
          placeholder="Enter an order ID"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      </div>

      {testOrderId && (
        <div>
          <h2 className="text-lg font-semibold mb-4">Payment Details for Order: {testOrderId}</h2>
          <PaymentDetailsCard orderId={testOrderId} />
        </div>
      )}

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">Test Instructions:</h3>
        <ol className="list-decimal list-inside text-sm text-blue-800 space-y-1">
          <li>Enter a valid order ID from your Firebase orders collection</li>
          <li>The component should fetch and display detailed payment information</li>
          <li>Check that Stripe data is being retrieved correctly</li>
          <li>Verify that the UI displays payment method, transaction details, and receipt links</li>
        </ol>
      </div>
    </div>
  );
};

export default PaymentDetailsTest;
