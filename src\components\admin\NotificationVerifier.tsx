import React, { useState } from 'react';
import { Search, CheckCircle, AlertCircle, User, Clock, Eye } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { collection, query, orderBy, limit, getDocs, doc, getDoc } from 'firebase/firestore';
import { firestore } from '../../firebase/config';

interface NotificationVerifierProps {
  className?: string;
}

interface NotificationData {
  id: string;
  type: string;
  title: string;
  message: string;
  createdAt: any;
  read: boolean;
  userId: string;
  userName?: string;
}

const NotificationVerifier: React.FC<NotificationVerifierProps> = ({ className = '' }) => {
  const { currentUser, isAdmin } = useAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<NotificationData[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchUserId, setSearchUserId] = useState('');

  const verifyNotifications = async (userId?: string) => {
    if (!currentUser || !isAdmin) {
      setError('Admin privileges required');
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      const targetUserId = userId || currentUser.uid;
      console.log('Checking notifications for user:', targetUserId);

      // Get user info
      const userDoc = await getDoc(doc(firestore, 'users', targetUserId));
      const userName = userDoc.exists() ? userDoc.data()?.name || userDoc.data()?.email : 'Unknown User';

      // Get notifications from user's subcollection
      const notificationsRef = collection(firestore, `users/${targetUserId}/notifications`);
      const q = query(notificationsRef, orderBy('createdAt', 'desc'), limit(10));
      
      const snapshot = await getDocs(q);
      
      const notifications: NotificationData[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        notifications.push({
          id: doc.id,
          type: data.type || 'unknown',
          title: data.title || 'No title',
          message: data.message || 'No message',
          createdAt: data.createdAt,
          read: data.read || false,
          userId: targetUserId,
          userName
        });
      });

      setResults(notifications);
      console.log(`Found ${notifications.length} notifications for user ${userName}`);

    } catch (err: any) {
      console.error('Error verifying notifications:', err);
      setError(`Error: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown time';
    
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleString();
    } catch {
      return 'Invalid date';
    }
  };

  if (!isAdmin) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
          <p className="text-sm text-yellow-600 dark:text-yellow-400">
            Admin privileges required to verify notifications
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center mb-4">
        <Eye className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Notification Verifier
        </h3>
      </div>

      <div className="space-y-4">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mr-2" />
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <input
            type="text"
            placeholder="User ID (leave empty to check your own)"
            value={searchUserId}
            onChange={(e) => setSearchUserId(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <button
            onClick={() => verifyNotifications(searchUserId.trim() || undefined)}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Search className="h-4 w-4 mr-2" />
            {loading ? 'Checking...' : 'Verify'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => verifyNotifications()}
            disabled={loading}
            className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <User className="h-4 w-4 mr-2" />
            Check My Notifications
          </button>

          <button
            onClick={() => verifyNotifications(currentUser?.uid)}
            disabled={loading}
            className="flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Refresh Check
          </button>
        </div>

        {results.length > 0 && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
              Found {results.length} notifications for {results[0]?.userName}:
            </h4>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {results.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border ${
                    notification.read
                      ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                      : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                          notification.type === 'admin_broadcast' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400' :
                          notification.type === 'admin_warning' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                        }`}>
                          {notification.type}
                        </span>
                        {!notification.read && (
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                        )}
                      </div>
                      <h5 className="font-medium text-gray-900 dark:text-white text-sm">
                        {notification.title}
                      </h5>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {notification.message}
                      </p>
                      <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(notification.createdAt)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {results.length === 0 && !loading && !error && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            Click "Verify" to check for notifications
          </div>
        )}

        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <p><strong>Current User:</strong> {currentUser?.email}</p>
          <p><strong>User ID:</strong> {currentUser?.uid}</p>
          <p><strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}</p>
        </div>
      </div>
    </div>
  );
};

export default NotificationVerifier;
