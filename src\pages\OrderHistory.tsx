import React, { useState, useEffect } from 'react';
import { Package, Clock, CheckCircle, XCircle, Eye, Calendar, DollarSign, User, ArrowLeft, Filter, Truck, MapPin, Lock, AlertCircle, Copy, FileText, Shield } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { formatPrice } from '../utils/priceUtils';
import { Order } from '../firebase/types';
import BuyerOrderCard from '../components/BuyerOrderCard';
import SellerOrderCard from '../components/SellerOrderCard';

const OrderHistory: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { getOrdersByBuyer, getOrdersBySeller, isLoading: checkoutLoading } = useStripeCheckout();

  // All state hooks declared at the top - NEVER conditional
  const [activeTab, setActiveTab] = useState<'all' | 'buyer' | 'seller'>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [orders, setOrders] = useState<Order[]>([]);
  const [buyerOrders, setBuyerOrders] = useState<Order[]>([]);
  const [sellerOrders, setSellerOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);

  // Load orders from Firebase
  useEffect(() => {
    const loadOrders = async () => {
      console.log('🚀 OrderHistory: Starting to load orders for user:', currentUser?.uid);

      if (!currentUser) {
        console.log('❌ OrderHistory: No current user, stopping load');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('📡 OrderHistory: Fetching buyer and seller orders...');

        // Fetch both buyer and seller orders
        const [buyerOrdersData, sellerOrdersData] = await Promise.all([
          getOrdersByBuyer(),
          getOrdersBySeller()
        ]);

        console.log('📦 OrderHistory: Raw data received:', {
          buyerOrders: buyerOrdersData.length,
          sellerOrders: sellerOrdersData.length
        });

        setBuyerOrders(buyerOrdersData);
        setSellerOrders(sellerOrdersData);

        // Combine all orders for 'all' tab, removing duplicates
        const allOrdersMap = new Map();

        // Add buyer orders first
        buyerOrdersData.forEach(order => {
          allOrdersMap.set(order.id, order);
        });

        // Add seller orders, but don't overwrite if already exists
        sellerOrdersData.forEach(order => {
          if (!allOrdersMap.has(order.id)) {
            allOrdersMap.set(order.id, order);
          }
        });

        const allOrders = Array.from(allOrdersMap.values());
        setOrders(allOrders);

        console.log(`✅ OrderHistory: Successfully loaded ${buyerOrdersData.length} buyer orders and ${sellerOrdersData.length} seller orders`);
      } catch (error) {
        console.error('❌ OrderHistory: Error loading orders:', error);
        setError(error instanceof Error ? error.message : 'Failed to load orders');
      } finally {
        console.log('🏁 OrderHistory: Finished loading orders');
        setIsLoading(false);
      }
    };

    loadOrders();
  }, [currentUser, getOrdersByBuyer, getOrdersBySeller]);

  // Refresh orders function
  const refreshOrders = async () => {
    if (!currentUser) return;
    
    setIsLoading(true);
    try {
      const [buyerOrdersData, sellerOrdersData] = await Promise.all([
        getOrdersByBuyer(),
        getOrdersBySeller()
      ]);

      setBuyerOrders(buyerOrdersData);
      setSellerOrders(sellerOrdersData);

      // Combine orders without duplicates
      const allOrdersMap = new Map();
      buyerOrdersData.forEach(order => allOrdersMap.set(order.id, order));
      sellerOrdersData.forEach(order => {
        if (!allOrdersMap.has(order.id)) {
          allOrdersMap.set(order.id, order);
        }
      });
      setOrders(Array.from(allOrdersMap.values()));
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to refresh orders');
    } finally {
      setIsLoading(false);
    }
  };

  // Get current orders based on active tab
  const getCurrentOrders = () => {
    switch (activeTab) {
      case 'buyer':
        return buyerOrders;
      case 'seller':
        return sellerOrders;
      default:
        return orders;
    }
  };

  const currentOrders = getCurrentOrders();

  const filteredOrders = currentOrders.filter(order => {
    if (statusFilter !== 'all') {
      return order.status === statusFilter;
    }
    return true;
  });

  // Calculate analytics based on buyer orders only
  const totalSpent = buyerOrders
    .filter(order => order.status === 'completed')
    .reduce((sum, order) => sum + (order.finalStripeAmount || order.amount || 0), 0);

  const totalPurchases = buyerOrders.filter(order => order.status === 'completed').length;

  const totalSales = sellerOrders
    .filter(order => order.status === 'completed')
    .reduce((sum, order) => sum + (order.sellerAmount || order.amount || 0), 0);

  const activeOrdersCount = [...buyerOrders, ...sellerOrders]
    .filter(o => ['pending_payment', 'in_progress', 'shipped', 'delivered'].includes(o.status)).length;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center overflow-x-hidden">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-16 h-16 bg-primary-200 dark:bg-primary-700 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Loading your orders...</div>
          <div className="text-xs text-gray-400 dark:text-gray-500 mt-2">
            User: {currentUser?.uid ? 'Authenticated' : 'Not authenticated'}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center overflow-x-hidden">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Error Loading Orders</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center overflow-x-hidden">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Please Log In</h3>
          <p className="text-gray-600 dark:text-gray-400">You need to be logged in to view your order history.</p>
        </div>
      </div>
    );
  }

  // Debug information - simple console log without useEffect
  if (orders.length > 0 && !isLoading) {
    console.log('OrderHistory loaded with', orders.length, 'orders');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-6xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Order History</h1>
          <p className="text-gray-600 dark:text-gray-400">Track your purchases and order status • Click any order to see detailed actions</p>
        </div>

        {/* Analytics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">${totalSpent.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                <Package className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Purchases</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalPurchases}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Sales Revenue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">${totalSales.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
                <Clock className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Active Orders</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{activeOrdersCount}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            {/* Tabs */}
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-xl p-1">
              {[
                { id: 'all', label: `All Orders (${orders.length})` },
                { id: 'buyer', label: `My Purchases (${buyerOrders.length})` },
                { id: 'seller', label: `My Sales (${sellerOrders.length})` }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    activeTab === tab.id
                      ? 'bg-white dark:bg-gray-800 text-primary-600 dark:text-primary-400 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Status Filter */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-gray-500" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending_payment">Pending Payment</option>
                  <option value="in_progress">In Progress</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="completed">Completed</option>
                  <option value="return_requested">Return Requested</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={refreshOrders}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  <Package className="w-4 h-4" />
                  <span>Refresh</span>
                </button>

                <button
                  onClick={() => {/* TODO: Implement PDF download */}}
                  className="flex items-center space-x-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                >
                  <FileText className="w-4 h-4" />
                  <span>Download PDF</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          </div>
        )}

        {/* Debug Information (only in development) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Debug Info</h4>
            <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <div>Current User: {currentUser?.uid || 'None'}</div>
              <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
              <div>Error: {error || 'None'}</div>
              <div>Total Orders: {orders.length}</div>
              <div>Buyer Orders: {buyerOrders.length}</div>
              <div>Seller Orders: {sellerOrders.length}</div>
              <div>Filtered Orders: {filteredOrders.length}</div>
              <div>Active Tab: {activeTab}</div>
              <div>Status Filter: {statusFilter}</div>
            </div>
          </div>
        )}

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.length > 0 ? (
            filteredOrders.map((order) => {
              // Determine if user is buyer or seller for this order
              const userRole = order.buyerId === currentUser?.uid ? 'buyer' : 'seller';

              return (
                <div key={order.id}>
                  {userRole === 'buyer' ? (
                    <BuyerOrderCard
                      order={order}
                      onOrderUpdate={refreshOrders}
                      isSelected={selectedOrderId === order.id}
                      onSelect={() => setSelectedOrderId(selectedOrderId === order.id ? null : order.id)}
                    />
                  ) : (
                    <SellerOrderCard
                      order={order}
                      onOrderUpdate={refreshOrders}
                      isSelected={selectedOrderId === order.id}
                      onSelect={() => setSelectedOrderId(selectedOrderId === order.id ? null : order.id)}
                    />
                  )}
                </div>
              );
            })
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-12 text-center">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Orders Found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {activeTab === 'buyer' 
                  ? "You haven't made any purchases yet."
                  : activeTab === 'seller'
                  ? "You haven't made any sales yet."
                  : "You don't have any orders yet."
                }
              </p>
              <Link
                to="/"
                className="inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                <span>{activeTab === 'seller' ? 'Start Selling' : 'Start Shopping'}</span>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderHistory;
