/**
 * 📲 FRONTEND/UX SECURITY VALIDATION SUITE
 * Hive Campus Production Frontend Testing
 * 
 * Tests frontend security:
 * - Input Validation & XSS Prevention
 * - FCM Notification Security
 * - Payment UI Security
 * - Admin Dashboard Access Control
 * - Client-Side Validation Bypass
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../src/contexts/AuthContext';

// Import components to test
import CreateListing from '../src/pages/CreateListing';
import UnifiedCheckout from '../src/components/UnifiedCheckout';
import AdminDashboard from '../src/pages/AdminDashboard';
import ChatWindow from '../src/components/ChatWindow';

// Mock Firebase
vi.mock('../src/firebase/config', () => ({
  auth: {
    currentUser: { uid: 'test-user-123', email: '<EMAIL>' }
  },
  db: {},
  storage: {}
}));

// Mock FCM
vi.mock('../src/services/fcm', () => ({
  requestNotificationPermission: vi.fn(),
  subscribeToTopic: vi.fn(),
  unsubscribeFromTopic: vi.fn()
}));

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      {children}
    </AuthProvider>
  </BrowserRouter>
);

describe('📲 FRONTEND SECURITY VALIDATION', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    // Reset all mocks
    vi.clearAllMocks();
  });

  describe('🛡️ INPUT VALIDATION & XSS PREVENTION', () => {
    it('should sanitize XSS attempts in listing title', async () => {
      render(
        <TestWrapper>
          <CreateListing />
        </TestWrapper>
      );

      const titleInput = screen.getByLabelText(/title/i);
      
      // Attempt XSS injection
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("XSS")',
        '<svg onload="alert(1)">',
        '"><script>alert("XSS")</script>'
      ];

      for (const payload of xssPayloads) {
        await user.clear(titleInput);
        await user.type(titleInput, payload);
        
        // Check that dangerous content is sanitized
        expect(titleInput.value).not.toContain('<script');
        expect(titleInput.value).not.toContain('javascript:');
        expect(titleInput.value).not.toContain('onerror=');
        expect(titleInput.value).not.toContain('onload=');
      }
    });

    it('should enforce max length limits on description field', async () => {
      render(
        <TestWrapper>
          <CreateListing />
        </TestWrapper>
      );

      const descriptionInput = screen.getByLabelText(/description/i);
      
      // Try to exceed 500 character limit
      const longText = 'A'.repeat(600);
      await user.type(descriptionInput, longText);
      
      // Should be truncated to 500 characters
      expect(descriptionInput.value.length).toBeLessThanOrEqual(500);
    });

    it('should validate price input against manipulation', async () => {
      render(
        <TestWrapper>
          <CreateListing />
        </TestWrapper>
      );

      const priceInput = screen.getByLabelText(/price/i);
      
      // Test invalid price inputs
      const invalidPrices = [
        '-100',      // Negative
        '0',         // Zero
        '10001',     // Above max
        'abc',       // Non-numeric
        '10.999',    // Too many decimals
        '1e10'       // Scientific notation
      ];

      for (const invalidPrice of invalidPrices) {
        await user.clear(priceInput);
        await user.type(priceInput, invalidPrice);
        
        // Submit form to trigger validation
        const submitButton = screen.getByRole('button', { name: /create listing/i });
        await user.click(submitButton);
        
        // Should show validation error
        await waitFor(() => {
          expect(screen.getByText(/invalid price/i)).toBeInTheDocument();
        });
      }
    });

    it('should prevent HTML injection in chat messages', async () => {
      const mockChat = {
        id: 'test-chat',
        participants: ['user1', 'user2'],
        messages: []
      };

      render(
        <TestWrapper>
          <ChatWindow chat={mockChat} />
        </TestWrapper>
      );

      const messageInput = screen.getByPlaceholderText(/type a message/i);
      
      // Attempt HTML injection
      const htmlPayloads = [
        '<b>Bold text</b>',
        '<a href="http://malicious.com">Click here</a>',
        '<iframe src="http://evil.com"></iframe>',
        '<form><input type="password"></form>'
      ];

      for (const payload of htmlPayloads) {
        await user.clear(messageInput);
        await user.type(messageInput, payload);
        
        const sendButton = screen.getByRole('button', { name: /send/i });
        await user.click(sendButton);
        
        // Message should be displayed as plain text, not rendered HTML
        await waitFor(() => {
          const messageElement = screen.getByText(payload);
          expect(messageElement.innerHTML).toBe(payload);
        });
      }
    });
  });

  describe('🔔 FCM NOTIFICATION SECURITY', () => {
    it('should validate notification permission flow', async () => {
      const mockRequestPermission = vi.fn().mockResolvedValue('granted');
      
      // Mock Notification API
      Object.defineProperty(window, 'Notification', {
        value: {
          requestPermission: mockRequestPermission,
          permission: 'default'
        }
      });

      render(
        <TestWrapper>
          <div data-testid="notification-settings">
            <button onClick={() => Notification.requestPermission()}>
              Enable Notifications
            </button>
          </div>
        </TestWrapper>
      );

      const enableButton = screen.getByText(/enable notifications/i);
      await user.click(enableButton);

      expect(mockRequestPermission).toHaveBeenCalled();
    });

    it('should ensure only intended users receive notifications', async () => {
      // Mock FCM token validation
      const mockValidateToken = vi.fn((token, userId) => {
        return token.includes(userId);
      });

      const validToken = 'fcm-token-user123-valid';
      const invalidToken = 'fcm-token-otheruser-invalid';
      const currentUserId = 'user123';

      expect(mockValidateToken(validToken, currentUserId)).toBe(true);
      expect(mockValidateToken(invalidToken, currentUserId)).toBe(false);
    });

    it('should test notification opt-in/out preferences', async () => {
      const mockUpdatePreferences = vi.fn();

      render(
        <TestWrapper>
          <div data-testid="notification-preferences">
            <label>
              <input 
                type="checkbox" 
                onChange={(e) => mockUpdatePreferences('chat', e.target.checked)}
              />
              Chat Notifications
            </label>
            <label>
              <input 
                type="checkbox" 
                onChange={(e) => mockUpdatePreferences('orders', e.target.checked)}
              />
              Order Notifications
            </label>
          </div>
        </TestWrapper>
      );

      const chatCheckbox = screen.getByLabelText(/chat notifications/i);
      const orderCheckbox = screen.getByLabelText(/order notifications/i);

      await user.click(chatCheckbox);
      await user.click(orderCheckbox);

      expect(mockUpdatePreferences).toHaveBeenCalledWith('chat', true);
      expect(mockUpdatePreferences).toHaveBeenCalledWith('orders', true);
    });
  });

  describe('💳 PAYMENT & WALLET UI SECURITY', () => {
    it('should prevent wallet balance manipulation in checkout', async () => {
      const mockListing = {
        id: 'test-listing',
        title: 'Test Item',
        price: 50,
        sellerId: 'seller123'
      };

      const mockWalletBalance = 30;

      render(
        <TestWrapper>
          <UnifiedCheckout 
            listing={mockListing} 
            walletBalance={mockWalletBalance}
          />
        </TestWrapper>
      );

      // Try to manipulate wallet credit input
      const walletCreditInput = screen.getByLabelText(/wallet credit/i);
      
      // Attempt to use more than available balance
      await user.clear(walletCreditInput);
      await user.type(walletCreditInput, '100'); // More than $30 balance

      const proceedButton = screen.getByRole('button', { name: /proceed/i });
      await user.click(proceedButton);

      // Should show error for insufficient balance
      await waitFor(() => {
        expect(screen.getByText(/insufficient wallet balance/i)).toBeInTheDocument();
      });
    });

    it('should validate Stripe checkout UI edge cases', async () => {
      const mockStripeElements = {
        create: vi.fn(() => ({
          mount: vi.fn(),
          on: vi.fn(),
          destroy: vi.fn()
        }))
      };

      // Mock Stripe
      vi.mock('@stripe/stripe-js', () => ({
        loadStripe: vi.fn(() => Promise.resolve({
          elements: vi.fn(() => mockStripeElements)
        }))
      }));

      render(
        <TestWrapper>
          <UnifiedCheckout 
            listing={{ id: 'test', price: 25, title: 'Test' }}
            walletBalance={0}
          />
        </TestWrapper>
      );

      // Test back button behavior
      const backButton = screen.getByRole('button', { name: /back/i });
      await user.click(backButton);

      // Should not proceed with incomplete payment
      expect(screen.queryByText(/payment processing/i)).not.toBeInTheDocument();
    });

    it('should prevent amount manipulation through browser dev tools', () => {
      // Test that payment amounts are validated server-side
      const clientAmount = 25.00;
      const serverAmount = 25.00;
      
      // Simulate client-side manipulation
      const manipulatedAmount = 1.00;
      
      // Server validation should catch this
      const isValidAmount = (client, server) => {
        return Math.abs(client - server) < 0.01; // Allow for floating point precision
      };

      expect(isValidAmount(clientAmount, serverAmount)).toBe(true);
      expect(isValidAmount(manipulatedAmount, serverAmount)).toBe(false);
    });
  });

  describe('🔐 ADMIN DASHBOARD ACCESS CONTROL', () => {
    it('should prevent unauthorized access to admin panels', async () => {
      // Mock non-admin user
      const mockNonAdminUser = {
        uid: 'regular-user-123',
        email: '<EMAIL>',
        role: 'student'
      };

      render(
        <TestWrapper>
          <AdminDashboard user={mockNonAdminUser} />
        </TestWrapper>
      );

      // Should show access denied message
      expect(screen.getByText(/access denied/i)).toBeInTheDocument();
      expect(screen.queryByText(/admin dashboard/i)).not.toBeInTheDocument();
    });

    it('should validate admin PIN entry security', async () => {
      const mockAdminUser = {
        uid: 'admin-user-123',
        email: '<EMAIL>',
        role: 'admin'
      };

      render(
        <TestWrapper>
          <AdminDashboard user={mockAdminUser} />
        </TestWrapper>
      );

      const pinInput = screen.getByLabelText(/admin pin/i);
      
      // Test PIN masking
      await user.type(pinInput, '123456789012');
      expect(pinInput.type).toBe('password');
      
      // Test minimum length requirement
      await user.clear(pinInput);
      await user.type(pinInput, '12345'); // Too short
      
      const submitButton = screen.getByRole('button', { name: /verify/i });
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/pin must be at least 12 digits/i)).toBeInTheDocument();
      });
    });

    it('should test admin dispute resolution interface', async () => {
      const mockDispute = {
        id: 'dispute-123',
        orderId: 'order-123',
        buyerId: 'buyer-123',
        sellerId: 'seller-123',
        reason: 'Item not received',
        status: 'open'
      };

      const mockAdminUser = {
        uid: 'admin-123',
        role: 'admin',
        verified: true
      };

      render(
        <TestWrapper>
          <AdminDashboard 
            user={mockAdminUser} 
            disputes={[mockDispute]}
          />
        </TestWrapper>
      );

      // Should show dispute resolution options
      expect(screen.getByText(/resolve dispute/i)).toBeInTheDocument();
      
      const refundButton = screen.getByRole('button', { name: /refund buyer/i });
      const rejectButton = screen.getByRole('button', { name: /reject dispute/i });
      
      expect(refundButton).toBeInTheDocument();
      expect(rejectButton).toBeInTheDocument();
    });
  });
});
