"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidStatusTransition = exports.SecureOrderStatusManager = exports.VALID_STATUS_TRANSITIONS = exports.OrderStatus = void 0;
const admin = __importStar(require("firebase-admin"));
const functions = __importStar(require("firebase-functions/v1"));
const schemas_1 = require("./validation/schemas");
const zod_1 = require("zod");
// Secure order status management for Hive Campus
// Valid order statuses
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING"] = "pending";
    OrderStatus["PAYMENT_PROCESSING"] = "payment_processing";
    OrderStatus["PAYMENT_SUCCEEDED"] = "payment_succeeded";
    OrderStatus["PAYMENT_FAILED"] = "payment_failed";
    OrderStatus["CONFIRMED"] = "confirmed";
    OrderStatus["SHIPPED"] = "shipped";
    OrderStatus["IN_TRANSIT"] = "in_transit";
    OrderStatus["DELIVERED"] = "delivered";
    OrderStatus["COMPLETED"] = "completed";
    OrderStatus["CANCELLED"] = "cancelled";
    OrderStatus["REFUNDED"] = "refunded";
    OrderStatus["DISPUTED"] = "disputed";
    OrderStatus["ADMIN_REVIEW"] = "admin_review";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
// Valid status transitions map - defines allowed state changes
exports.VALID_STATUS_TRANSITIONS = {
    [OrderStatus.PENDING]: [
        OrderStatus.PAYMENT_PROCESSING,
        OrderStatus.CANCELLED
    ],
    [OrderStatus.PAYMENT_PROCESSING]: [
        OrderStatus.PAYMENT_SUCCEEDED,
        OrderStatus.PAYMENT_FAILED,
        OrderStatus.CANCELLED
    ],
    [OrderStatus.PAYMENT_SUCCEEDED]: [
        OrderStatus.CONFIRMED,
        OrderStatus.REFUNDED,
        OrderStatus.CANCELLED
    ],
    [OrderStatus.PAYMENT_FAILED]: [
        OrderStatus.PENDING,
        OrderStatus.CANCELLED
    ],
    [OrderStatus.CONFIRMED]: [
        OrderStatus.SHIPPED,
        OrderStatus.CANCELLED,
        OrderStatus.REFUNDED
    ],
    [OrderStatus.SHIPPED]: [
        OrderStatus.IN_TRANSIT,
        OrderStatus.DELIVERED,
        OrderStatus.DISPUTED
    ],
    [OrderStatus.IN_TRANSIT]: [
        OrderStatus.DELIVERED,
        OrderStatus.DISPUTED
    ],
    [OrderStatus.DELIVERED]: [
        OrderStatus.COMPLETED,
        OrderStatus.DISPUTED
    ],
    [OrderStatus.COMPLETED]: [
        OrderStatus.DISPUTED // Only disputes allowed after completion
    ],
    [OrderStatus.CANCELLED]: [], // Terminal state
    [OrderStatus.REFUNDED]: [], // Terminal state
    [OrderStatus.DISPUTED]: [
        OrderStatus.COMPLETED,
        OrderStatus.REFUNDED,
        OrderStatus.ADMIN_REVIEW
    ],
    [OrderStatus.ADMIN_REVIEW]: [
        OrderStatus.COMPLETED,
        OrderStatus.REFUNDED,
        OrderStatus.CANCELLED
    ]
};
// Order status update schema
const OrderStatusUpdateSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1, 'Order ID is required'),
    newStatus: zod_1.z.nativeEnum(OrderStatus),
    reason: zod_1.z.string().max(500).optional(),
    adminOverride: zod_1.z.boolean().default(false),
    proofOfDelivery: zod_1.z.object({
        type: zod_1.z.enum(['fcm_confirmation', 'user_confirmation', 'tracking_update']),
        timestamp: zod_1.z.string(),
        metadata: zod_1.z.record(zod_1.z.unknown()).optional()
    }).optional()
});
/**
 * Secure order status manager with validation and audit trail
 */
class SecureOrderStatusManager {
    /**
     * Validate if a status transition is allowed
     */
    static isValidTransition(currentStatus, newStatus, isAdminOverride = false) {
        // Admin override allows any transition except to/from terminal states
        if (isAdminOverride) {
            const terminalStates = [OrderStatus.CANCELLED, OrderStatus.REFUNDED];
            // Don't allow admin to override terminal states unless going to admin review
            if (terminalStates.includes(currentStatus) && newStatus !== OrderStatus.ADMIN_REVIEW) {
                return false;
            }
            return true;
        }
        // Check if transition is in the allowed list
        const allowedTransitions = exports.VALID_STATUS_TRANSITIONS[currentStatus] || [];
        return allowedTransitions.includes(newStatus);
    }
    /**
     * Update order status with validation and audit trail
     */
    static async updateOrderStatus(orderId, newStatus, updatedBy, options = {}) {
        try {
            // Validate input
            const validatedData = (0, schemas_1.validateInput)(OrderStatusUpdateSchema, {
                orderId,
                newStatus,
                reason: options.reason,
                adminOverride: options.adminOverride || false,
                proofOfDelivery: options.proofOfDelivery
            });
            const orderRef = admin.firestore().collection('orders').doc(orderId);
            const result = await admin.firestore().runTransaction(async (transaction) => {
                const orderDoc = await transaction.get(orderRef);
                if (!orderDoc.exists) {
                    throw new functions.https.HttpsError('not-found', 'Order not found');
                }
                const orderData = orderDoc.data();
                const currentStatus = orderData === null || orderData === void 0 ? void 0 : orderData.status;
                // Skip validation for same status (idempotent)
                if (currentStatus === newStatus) {
                    return { success: true, message: 'Status unchanged' };
                }
                // Validate transition
                const isValidTransition = this.isValidTransition(currentStatus, newStatus, validatedData.adminOverride);
                if (!isValidTransition) {
                    const errorMsg = `Invalid status transition: ${currentStatus} → ${newStatus}`;
                    console.error(`❌ ${errorMsg} for order ${orderId}`);
                    // Log invalid transition attempt
                    await this.logStatusAttempt(orderId, currentStatus, newStatus, updatedBy, false, validatedData.reason);
                    throw new functions.https.HttpsError('failed-precondition', errorMsg);
                }
                // Create status history entry
                const statusHistory = {
                    status: newStatus,
                    timestamp: admin.firestore.Timestamp.now(),
                    updatedBy,
                    reason: validatedData.reason,
                    adminOverride: validatedData.adminOverride,
                    proofOfDelivery: validatedData.proofOfDelivery
                };
                // Update order with new status and history
                const updateData = {
                    status: newStatus,
                    updatedAt: admin.firestore.Timestamp.now(),
                    statusHistory: admin.firestore.FieldValue.arrayUnion(statusHistory)
                };
                // Add specific timestamps for key statuses
                switch (newStatus) {
                    case OrderStatus.PAYMENT_SUCCEEDED:
                        updateData.paidAt = admin.firestore.Timestamp.now();
                        break;
                    case OrderStatus.SHIPPED:
                        updateData.shippedAt = admin.firestore.Timestamp.now();
                        break;
                    case OrderStatus.DELIVERED:
                        updateData.deliveredAt = admin.firestore.Timestamp.now();
                        if (validatedData.proofOfDelivery) {
                            updateData.proofOfDelivery = validatedData.proofOfDelivery;
                        }
                        break;
                    case OrderStatus.COMPLETED:
                        updateData.completedAt = admin.firestore.Timestamp.now();
                        updateData.fundsReleased = true;
                        updateData.fundsReleasedAt = admin.firestore.Timestamp.now();
                        break;
                    case OrderStatus.CANCELLED:
                    case OrderStatus.REFUNDED:
                        updateData.cancelledAt = admin.firestore.Timestamp.now();
                        break;
                }
                transaction.update(orderRef, updateData);
                // Log successful transition
                await this.logStatusAttempt(orderId, currentStatus, newStatus, updatedBy, true, validatedData.reason);
                console.log(`✅ Order ${orderId} status updated: ${currentStatus} → ${newStatus} by ${updatedBy}`);
                return { success: true, message: `Status updated to ${newStatus}` };
            });
            // Trigger post-update actions
            await this.handlePostStatusUpdate(orderId, newStatus, options);
            return result;
        }
        catch (error) {
            console.error('❌ Error updating order status:', error);
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            throw new functions.https.HttpsError('internal', 'Failed to update order status');
        }
    }
    /**
     * Log status transition attempts for audit trail
     */
    static async logStatusAttempt(orderId, fromStatus, toStatus, updatedBy, success, reason) {
        try {
            await admin.firestore().collection('orderStatusLogs').add({
                orderId,
                fromStatus,
                toStatus,
                updatedBy,
                success,
                reason,
                timestamp: admin.firestore.Timestamp.now(),
                ip: 'system' // Could be enhanced to capture actual IP
            });
        }
        catch (error) {
            console.error('❌ Error logging status attempt:', error);
            // Don't throw - logging failure shouldn't break the main operation
        }
    }
    /**
     * Handle post-status update actions (notifications, etc.)
     */
    static async handlePostStatusUpdate(orderId, newStatus, options) {
        try {
            // Trigger notifications based on status
            switch (newStatus) {
                case OrderStatus.SHIPPED:
                    // Send shipping notification to buyer
                    break;
                case OrderStatus.DELIVERED:
                    // Start 3-day auto-release timer
                    // Send delivery confirmation to buyer
                    break;
                case OrderStatus.DISPUTED:
                    // Notify admin of dispute
                    // Create dispute record
                    break;
                case OrderStatus.COMPLETED:
                    // Release funds to seller
                    // Send completion notifications
                    break;
            }
        }
        catch (error) {
            console.error('❌ Error in post-status update actions:', error);
            // Don't throw - these are non-critical actions
        }
    }
    /**
     * Get order status history
     */
    static async getOrderStatusHistory(orderId) {
        try {
            const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
            if (!orderDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'Order not found');
            }
            const orderData = orderDoc.data();
            return (orderData === null || orderData === void 0 ? void 0 : orderData.statusHistory) || [];
        }
        catch (error) {
            console.error('❌ Error getting order status history:', error);
            throw new functions.https.HttpsError('internal', 'Failed to get order history');
        }
    }
    /**
     * Check if order can be transitioned to a specific status
     */
    static canTransitionTo(currentStatus, targetStatus, isAdmin = false) {
        return this.isValidTransition(currentStatus, targetStatus, isAdmin);
    }
}
exports.SecureOrderStatusManager = SecureOrderStatusManager;
// Export for use in Firestore rules
const isValidStatusTransition = (currentStatus, newStatus) => {
    return SecureOrderStatusManager.isValidTransition(currentStatus, newStatus, false);
};
exports.isValidStatusTransition = isValidStatusTransition;
