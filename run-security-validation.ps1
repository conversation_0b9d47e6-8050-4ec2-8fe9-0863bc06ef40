# Hive Campus Comprehensive Security Validation Script
# PowerShell version for cross-platform compatibility

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "🔐 HIVE CAMPUS COMPREHENSIVE SECURITY VALIDATION" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎯 Objective: Complete security, logic, and production validation" -ForegroundColor Green
Write-Host "📅 Started: $(Get-Date)" -ForegroundColor Green
Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Function to run command and check result
function Invoke-ValidationStep {
    param(
        [string]$StepName,
        [string]$Command,
        [bool]$Critical = $true
    )
    
    Write-Host "Running: $StepName" -ForegroundColor Yellow
    
    try {
        if ($Command.StartsWith("npm ")) {
            $result = Invoke-Expression $Command
            $exitCode = $LASTEXITCODE
        } else {
            $result = Invoke-Expression "node $Command"
            $exitCode = $LASTEXITCODE
        }
        
        if ($exitCode -eq 0) {
            Write-Host "✅ $StepName completed successfully" -ForegroundColor Green
            return $true
        } else {
            if ($Critical) {
                Write-Host "❌ $StepName failed (Critical)" -ForegroundColor Red
            } else {
                Write-Host "⚠️ $StepName failed (Non-critical)" -ForegroundColor Yellow
            }
            return $false
        }
    }
    catch {
        if ($Critical) {
            Write-Host "❌ $StepName failed with error: $($_.Exception.Message)" -ForegroundColor Red
        } else {
            Write-Host "⚠️ $StepName failed with error: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        return $false
    }
}

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Cyan

try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
}
catch {
    Write-Host "❌ Node.js not found. Please install Node.js first." -ForegroundColor Red
    exit 1
}

try {
    $npmVersion = npm --version
    Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green
}
catch {
    Write-Host "❌ npm not found. Please install npm first." -ForegroundColor Red
    exit 1
}

Write-Host ""

# Install dependencies if needed
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    $installResult = Invoke-ValidationStep "Dependency Installation" "npm install"
    if (-not $installResult) {
        Write-Host "❌ Failed to install dependencies. Exiting." -ForegroundColor Red
        exit 1
    }
}

Write-Host ""

# Build the application
Write-Host "🏗️ Building application..." -ForegroundColor Cyan
$buildResult = Invoke-ValidationStep "Application Build" "npm run build"
if (-not $buildResult) {
    Write-Host "❌ Build failed. Cannot proceed with validation." -ForegroundColor Red
    exit 1
}

Write-Host ""

# Initialize results tracking
$validationResults = @{
    SecurityAudit = $false
    LoadTesting = $false
    ProductionValidation = $false
    UnitTests = $false
    E2ETests = $false
    PerformanceAudit = $false
    ComprehensiveReport = $false
}

# Phase 1: Security Validation
Write-Host "🛡️ PHASE 1: SECURITY VALIDATION" -ForegroundColor Magenta
Write-Host "----------------------------------------" -ForegroundColor Gray
$validationResults.SecurityAudit = Invoke-ValidationStep "Security Audit" "scripts/comprehensive-security-audit.js"
Write-Host ""

# Phase 2: Load Testing
Write-Host "📈 PHASE 2: LOAD TESTING & ABUSE SIMULATION" -ForegroundColor Magenta
Write-Host "----------------------------------------" -ForegroundColor Gray
$validationResults.LoadTesting = Invoke-ValidationStep "Load Testing" "scripts/load-testing.js"
Write-Host ""

# Phase 3: Production Validation
Write-Host "🚀 PHASE 3: PRODUCTION DEPLOYMENT VALIDATION" -ForegroundColor Magenta
Write-Host "----------------------------------------" -ForegroundColor Gray
$validationResults.ProductionValidation = Invoke-ValidationStep "Production Validation" "scripts/production-deployment-checklist.js"
Write-Host ""

# Phase 4: Unit Tests
Write-Host "🧪 PHASE 4: UNIT & INTEGRATION TESTS" -ForegroundColor Magenta
Write-Host "----------------------------------------" -ForegroundColor Gray
$validationResults.UnitTests = Invoke-ValidationStep "Unit Tests" "npm run test:coverage" $false
Write-Host ""

# Phase 5: E2E Tests (optional)
Write-Host "🎭 PHASE 5: END-TO-END TESTS" -ForegroundColor Magenta
Write-Host "----------------------------------------" -ForegroundColor Gray
$validationResults.E2ETests = Invoke-ValidationStep "E2E Tests" "npm run test:e2e" $false
Write-Host ""

# Phase 6: Performance Audit (optional)
Write-Host "⚡ PHASE 6: PERFORMANCE AUDIT" -ForegroundColor Magenta
Write-Host "----------------------------------------" -ForegroundColor Gray
$validationResults.PerformanceAudit = Invoke-ValidationStep "Performance Audit" "npm run lighthouse" $false
Write-Host ""

# Final Comprehensive Report
Write-Host "🎯 GENERATING FINAL COMPREHENSIVE REPORT" -ForegroundColor Magenta
Write-Host "----------------------------------------" -ForegroundColor Gray
$validationResults.ComprehensiveReport = Invoke-ValidationStep "Comprehensive Report" "scripts/run-comprehensive-validation.js"
Write-Host ""

# Display final results
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "🏆 HIVE CAMPUS SECURITY VALIDATION COMPLETE" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Calculate success rate
$totalTests = $validationResults.Count
$passedTests = ($validationResults.Values | Where-Object { $_ -eq $true }).Count
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)

Write-Host "📊 VALIDATION RESULTS SUMMARY:" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Gray
Write-Host "Total Validation Steps: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $($totalTests - $passedTests)" -ForegroundColor Red
Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })
Write-Host ""

# Individual results
Write-Host "📋 DETAILED RESULTS:" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Gray
foreach ($test in $validationResults.GetEnumerator()) {
    $status = if ($test.Value) { "✅ PASSED" } else { "❌ FAILED" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "$($test.Key): $status" -ForegroundColor $color
}
Write-Host ""

# Check for generated reports
Write-Host "📄 GENERATED REPORTS:" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Gray

$reportTypes = @(
    @{ Pattern = "security-audit-*.json"; Name = "Security Audit" },
    @{ Pattern = "load-test-report-*.json"; Name = "Load Testing" },
    @{ Pattern = "production-deployment-report-*.json"; Name = "Production Validation" },
    @{ Pattern = "hive-campus-validation-report-*.json"; Name = "Comprehensive Report" }
)

foreach ($reportType in $reportTypes) {
    $reports = Get-ChildItem -Path . -Name $reportType.Pattern | Sort-Object LastWriteTime -Descending
    if ($reports.Count -gt 0) {
        Write-Host "✅ $($reportType.Name): $($reports[0])" -ForegroundColor Green
    } else {
        Write-Host "❌ $($reportType.Name): No report found" -ForegroundColor Red
    }
}

Write-Host ""

# Final assessment
Write-Host "🎯 FINAL ASSESSMENT:" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Gray

if ($successRate -ge 95) {
    Write-Host "🏆 EXCELLENT! Hive Campus is PRODUCTION CERTIFIED" -ForegroundColor Green
    Write-Host "✅ Ready for immediate production deployment" -ForegroundColor Green
} elseif ($successRate -ge 85) {
    Write-Host "🥈 GOOD! Hive Campus is PRODUCTION READY" -ForegroundColor Yellow
    Write-Host "✅ Ready for deployment with minor optimizations" -ForegroundColor Yellow
} elseif ($successRate -ge 70) {
    Write-Host "🥉 ACCEPTABLE! Hive Campus NEEDS MINOR FIXES" -ForegroundColor Yellow
    Write-Host "⚠️ Address minor issues before deployment" -ForegroundColor Yellow
} else {
    Write-Host "❌ NEEDS IMPROVEMENT! Hive Campus is NOT READY" -ForegroundColor Red
    Write-Host "🚨 Significant improvements required before deployment" -ForegroundColor Red
}

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "📞 NEXT STEPS:" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Review all generated reports for detailed findings" -ForegroundColor White
Write-Host "2. Address any critical issues identified" -ForegroundColor White
Write-Host "3. Re-run validation if fixes were applied" -ForegroundColor White
Write-Host "4. Deploy to production once certified" -ForegroundColor White
Write-Host ""
Write-Host "📖 For detailed guidance, refer to:" -ForegroundColor Cyan
Write-Host "   COMPREHENSIVE_SECURITY_VALIDATION_SUMMARY.md" -ForegroundColor White
Write-Host ""
Write-Host "🎉 Validation Complete! Thank you for using Hive Campus Security Validation." -ForegroundColor Green
Write-Host ""

# Keep window open
Read-Host "Press Enter to exit"
