#!/usr/bin/env node

/**
 * Database Content Verification Script
 * 
 * This script checks what data exists in Firestore collections
 * to verify if mock data cleanup is needed.
 * 
 * Usage: node scripts/verify-database-content.js
 */

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  getDocs,
  connectFirestoreEmulator
} from 'firebase/firestore';

// Firebase configuration from environment variables
// Load from .env file for scripts
import dotenv from 'dotenv';
dotenv.config();

const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY || "FIREBASE_API_KEY_PLACEHOLDER",
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN || "FIREBASE_AUTH_DOMAIN_PLACEHOLDER",
  projectId: process.env.VITE_FIREBASE_PROJECT_ID || "FIREBASE_PROJECT_ID_PLACEHOLDER",
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET || "FIREBASE_STORAGE_BUCKET_PLACEHOLDER",
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "FIREBASE_MESSAGING_SENDER_ID_PLACEHOLDER",
  appId: process.env.VITE_FIREBASE_APP_ID || "FIREBASE_APP_ID_PLACEHOLDER",
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID || "FIREBASE_MEASUREMENT_ID_PLACEHOLDER"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Function to check collection content
async function checkCollection(collectionName) {
  console.log(`\n📋 Checking ${collectionName} collection...`);
  
  try {
    const collectionRef = collection(db, collectionName);
    const snapshot = await getDocs(collectionRef);
    
    console.log(`  📊 Total documents: ${snapshot.size}`);
    
    if (snapshot.size > 0) {
      console.log(`  📄 Sample documents:`);
      let count = 0;
      snapshot.docs.forEach(doc => {
        if (count < 3) { // Show first 3 documents
          const data = doc.data();
          console.log(`    - ID: ${doc.id}`);
          console.log(`      Title/Name: ${data.title || data.name || data.email || 'N/A'}`);
          console.log(`      Type: ${data.type || data.category || 'N/A'}`);
          count++;
        }
      });
      
      if (snapshot.size > 3) {
        console.log(`    ... and ${snapshot.size - 3} more documents`);
      }
    } else {
      console.log(`  ✨ Collection is empty`);
    }
    
    return snapshot.size;
  } catch (error) {
    console.error(`  ❌ Error accessing ${collectionName}:`, error.message);
    return 0;
  }
}

// Function to identify potential mock data
async function identifyMockData(collectionName) {
  console.log(`\n🔍 Identifying potential mock data in ${collectionName}...`);
  
  const mockPatterns = {
    users: ['test', 'demo', 'mock', 'fake', 'john.doe', 'alex.johnson', 'sarah.chen'],
    listings: ['iPhone', 'MacBook', 'Calculus', 'Gaming Setup', 'Nike Air Force', 'Organic Chemistry']
  };
  
  try {
    const collectionRef = collection(db, collectionName);
    const snapshot = await getDocs(collectionRef);
    
    let mockCount = 0;
    const patterns = mockPatterns[collectionName] || [];
    
    snapshot.docs.forEach(doc => {
      const data = doc.data();
      const searchText = JSON.stringify(data).toLowerCase();
      
      const isMock = patterns.some(pattern => 
        searchText.includes(pattern.toLowerCase())
      );
      
      if (isMock) {
        mockCount++;
        console.log(`  🎭 Potential mock data: ${doc.id} - ${data.title || data.name || data.email || 'Unknown'}`);
      }
    });
    
    if (mockCount === 0) {
      console.log(`  ✅ No obvious mock data patterns found`);
    } else {
      console.log(`  ⚠️  Found ${mockCount} potential mock documents`);
    }
    
    return mockCount;
  } catch (error) {
    console.error(`  ❌ Error checking mock data in ${collectionName}:`, error.message);
    return 0;
  }
}

// Main verification function
async function verifyDatabaseContent() {
  console.log('🔍 Starting database content verification...');
  console.log('📊 This will check what data exists in your Firestore collections\n');
  
  const collections = ['users', 'listings', 'chats', 'analytics'];
  let totalDocs = 0;
  let totalMockDocs = 0;
  
  // Check each collection
  for (const collectionName of collections) {
    const docCount = await checkCollection(collectionName);
    totalDocs += docCount;
    
    if (docCount > 0) {
      const mockCount = await identifyMockData(collectionName);
      totalMockDocs += mockCount;
    }
  }
  
  console.log('\n📈 Summary:');
  console.log(`  📊 Total documents across all collections: ${totalDocs}`);
  console.log(`  🎭 Potential mock documents: ${totalMockDocs}`);
  
  if (totalMockDocs > 0) {
    console.log('\n⚠️  Recommendation: Run cleanup script to remove mock data');
    console.log('   Command: npm run cleanup:mock-data-cli');
  } else {
    console.log('\n✅ Database appears to be clean of mock data');
  }
  
  if (totalDocs === 0) {
    console.log('\n💡 Note: Database appears to be empty. This is normal for a fresh setup.');
  }
}

// Run the verification
verifyDatabaseContent()
  .then(() => {
    console.log('\n👋 Database verification completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Database verification failed:', error);
    process.exit(1);
  });

export { verifyDatabaseContent };
