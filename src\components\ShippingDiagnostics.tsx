import React from 'react';
import { AlertCircle, CheckCircle, Package, MapPin, Truck } from 'lucide-react';

interface ShippingDiagnosticsProps {
  listing: any;
  className?: string;
}

const ShippingDiagnostics: React.FC<ShippingDiagnosticsProps> = ({ 
  listing, 
  className = "" 
}) => {
  // Check delivery method
  const isMailDelivery = listing?.deliveryMethod === 'mail';
  const isShippoModel = listing?.shippingOptions?.model === 'shippo';
  
  // Check package details
  const hasPackageDetails = !!(
    listing?.shippingOptions?.packageDetails?.presetUsed ||
    (listing?.shippingOptions?.packageDetails?.dimensions && 
     listing?.shippingOptions?.packageDetails?.weight)
  );
  
  // Check seller address
  const sellerAddress = listing?.shippingOptions?.sellerAddress;
  const hasSellerAddress = !!(
    sellerAddress?.name &&
    sellerAddress?.street1 &&
    sellerAddress?.city &&
    sellerAddress?.state &&
    sellerAddress?.zip
  );
  
  // Legacy package size fallback
  const hasLegacyPackageSize = !!listing?.shippingOptions?.packageSize;
  
  const canLoadShippingRates = isMailDelivery && isShippoModel && 
    (hasPackageDetails || hasLegacyPackageSize) && hasSellerAddress;

  const StatusIcon: React.FC<{ status: boolean }> = ({ status }) => (
    status ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <AlertCircle className="w-5 h-5 text-red-500" />
    )
  );

  if (!isMailDelivery || !isShippoModel) {
    return null; // Only show for Shippo mail delivery
  }

  return (
    <div className={`bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-center space-x-2 mb-3">
        <Truck className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        <h4 className="font-medium text-blue-900 dark:text-blue-100">
          Shipping Configuration Status
        </h4>
      </div>
      
      <div className="space-y-3">
        {/* Delivery Method */}
        <div className="flex items-center space-x-3">
          <StatusIcon status={isMailDelivery} />
          <div className="flex-1">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Mail Delivery
            </span>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {isMailDelivery ? 'Configured for mail delivery' : 'Set to in-person pickup'}
            </p>
          </div>
        </div>

        {/* Shipping Model */}
        <div className="flex items-center space-x-3">
          <StatusIcon status={isShippoModel} />
          <div className="flex-1">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Hive Shipping (Shippo)
            </span>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {isShippoModel ? 'Using Hive Shipping for real-time rates' : 'Using manual shipping'}
            </p>
          </div>
        </div>

        {/* Package Details */}
        <div className="flex items-center space-x-3">
          <StatusIcon status={hasPackageDetails || hasLegacyPackageSize} />
          <div className="flex-1">
            <div className="flex items-center space-x-1">
              <Package className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Package Information
              </span>
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {hasPackageDetails ? (
                listing.shippingOptions.packageDetails.presetUsed ? 
                  `Preset: ${listing.shippingOptions.packageDetails.presetUsed}` :
                  'Custom dimensions and weight configured'
              ) : hasLegacyPackageSize ? (
                `Legacy size: ${listing.shippingOptions.packageSize}`
              ) : (
                'Package dimensions and weight not configured'
              )}
            </p>
          </div>
        </div>

        {/* Seller Address */}
        <div className="flex items-center space-x-3">
          <StatusIcon status={hasSellerAddress} />
          <div className="flex-1">
            <div className="flex items-center space-x-1">
              <MapPin className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Seller Address
              </span>
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {hasSellerAddress ? (
                `${sellerAddress.name}, ${sellerAddress.city}, ${sellerAddress.state} ${sellerAddress.zip}`
              ) : (
                'Seller shipping address not configured'
              )}
            </p>
          </div>
        </div>

        {/* Overall Status */}
        <div className={`mt-4 p-3 rounded-lg ${
          canLoadShippingRates 
            ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
            : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
        }`}>
          <div className="flex items-center space-x-2">
            <StatusIcon status={canLoadShippingRates} />
            <span className={`text-sm font-medium ${
              canLoadShippingRates 
                ? 'text-green-800 dark:text-green-200'
                : 'text-red-800 dark:text-red-200'
            }`}>
              {canLoadShippingRates 
                ? '✅ Ready for real-time shipping rates'
                : '❌ Cannot load shipping rates'
              }
            </span>
          </div>
          {!canLoadShippingRates && (
            <p className="text-xs text-red-600 dark:text-red-400 mt-1">
              Please ensure package details and seller address are configured to enable real-time shipping rates.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShippingDiagnostics;
