import React, { useState, useEffect } from 'react';
import { 
  Package, 
  DollarSign, 
  ShoppingBag, 
  TrendingUp, 
  Filter,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  RotateCcw,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useOrderAnalytics } from '../hooks/useOrderAnalytics';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { Order } from '../firebase/types';
import BuyerOrderCard from '../components/BuyerOrderCard';
import SellerOrderCard from '../components/SellerOrderCard';
import { formatPrice } from '../utils/priceUtils';

type TabType = 'all' | 'purchases' | 'sales';
type StatusFilter = 'all' | 'pending_payment' | 'payment_completed' | 'delivered' | 'returned' | 'cancelled';

const OrdersManagement: React.FC = () => {
  const { currentUser } = useAuth();
  const { analytics, isLoading: analyticsLoading } = useOrderAnalytics();
  const { getOrdersByBuyer, getOrdersBySeller, isLoading: ordersLoading } = useStripeCheckout();

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('all');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [orders, setOrders] = useState<Order[]>([]);
  const [buyerOrders, setBuyerOrders] = useState<Order[]>([]);
  const [sellerOrders, setSellerOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);

  // Load orders on component mount
  useEffect(() => {
    loadOrders();
  }, [currentUser]);

  const loadOrders = async () => {
    if (!currentUser) return;

    setIsLoading(true);
    setError(null);

    try {
      const [buyerOrdersData, sellerOrdersData] = await Promise.all([
        getOrdersByBuyer(),
        getOrdersBySeller()
      ]);

      setBuyerOrders(buyerOrdersData);
      setSellerOrders(sellerOrdersData);

      // Combine all orders for 'all' tab, removing duplicates
      const allOrdersMap = new Map();
      buyerOrdersData.forEach(order => allOrdersMap.set(order.id, order));
      sellerOrdersData.forEach(order => {
        if (!allOrdersMap.has(order.id)) {
          allOrdersMap.set(order.id, order);
        }
      });
      setOrders(Array.from(allOrdersMap.values()));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load orders');
    } finally {
      setIsLoading(false);
    }
  };

  // Get filtered orders based on active tab and status filter
  const getFilteredOrders = () => {
    let ordersToFilter: Order[] = [];

    switch (activeTab) {
      case 'purchases':
        ordersToFilter = buyerOrders;
        break;
      case 'sales':
        ordersToFilter = sellerOrders;
        break;
      default:
        ordersToFilter = orders;
    }

    if (statusFilter === 'all') {
      return ordersToFilter;
    }

    return ordersToFilter.filter(order => order.status === statusFilter);
  };

  const filteredOrders = getFilteredOrders();

  // Status filter options
  const statusOptions = [
    { value: 'all', label: 'All Statuses', icon: Package },
    { value: 'pending_payment', label: 'Pending Payment', icon: Clock },
    { value: 'payment_completed', label: 'Payment Completed', icon: CheckCircle },
    { value: 'delivered', label: 'Delivered', icon: Package },
    { value: 'returned', label: 'Returned', icon: RotateCcw },
    { value: 'cancelled', label: 'Cancelled', icon: XCircle },
  ];

  if (isLoading || analyticsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Order History
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track your purchases and order status • Click any order to see detailed actions
          </p>
        </div>

        {/* Analytics Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${formatPrice(analytics?.totalSpent || 0)}
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Purchases</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {analytics?.totalPurchases || 0}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <ShoppingBag className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sales Revenue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${formatPrice(analytics?.totalRevenue || 0)}
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Listings Sold</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {analytics?.totalSales || 0}
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <Package className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {[
                { key: 'all', label: 'All Orders', count: orders.length },
                { key: 'purchases', label: 'My Purchases', count: buyerOrders.length },
                { key: 'sales', label: 'My Sales', count: sellerOrders.length },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as TabType)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  {tab.label}
                  <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>

          {/* Status Filter Bar */}
          <div className="p-6">
            <div className="flex items-center space-x-4 overflow-x-auto">
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">
                <Filter className="w-4 h-4" />
                <span>Filter by status:</span>
              </div>
              <div className="flex space-x-2">
                {statusOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <button
                      key={option.value}
                      onClick={() => setStatusFilter(option.value as StatusFilter)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                        statusFilter === option.value
                          ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      <IconComponent className="w-4 h-4" />
                      <span>{option.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
                <p className="text-red-700 dark:text-red-300">{error}</p>
              </div>
            </div>
          )}

          {filteredOrders.length > 0 ? (
            filteredOrders.map((order) => {
              const userRole = order.buyerId === currentUser?.uid ? 'buyer' : 'seller';
              return (
                <div key={order.id}>
                  {userRole === 'buyer' ? (
                    <BuyerOrderCard
                      order={order}
                      onOrderUpdate={loadOrders}
                      isSelected={selectedOrderId === order.id}
                      onSelect={() => setSelectedOrderId(selectedOrderId === order.id ? null : order.id)}
                    />
                  ) : (
                    <SellerOrderCard
                      order={order}
                      onOrderUpdate={loadOrders}
                      isSelected={selectedOrderId === order.id}
                      onSelect={() => setSelectedOrderId(selectedOrderId === order.id ? null : order.id)}
                    />
                  )}
                </div>
              );
            })
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-12 text-center shadow-sm border border-gray-200 dark:border-gray-700">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No orders found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {statusFilter === 'all' 
                  ? "You haven't made any orders yet."
                  : `No orders found with status "${statusOptions.find(opt => opt.value === statusFilter)?.label}".`
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrdersManagement;
