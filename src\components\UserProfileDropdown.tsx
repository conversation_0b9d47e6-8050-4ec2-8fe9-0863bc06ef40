import React, { useState, useRef, useEffect } from 'react';
import {
  User,
  Settings,
  MessageSquare,
  Phone,
  Info,
  AlertTriangle,
  ChevronDown,
  LogOut,
  Shield,
  Package,
  Star,
  Flag
} from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { logOut } from '../firebase/auth';
import { useStripeConnect } from '../hooks/useStripeConnect';
import FeedbackModal from './FeedbackModal';
import ComplaintModal from './ComplaintModal';
import OnboardingStatusBadge from './OnboardingStatusBadge';

interface UserProfileDropdownProps {
  user: {
    name: string;
    email: string;
    avatar: string;
    university: string;
    verified: boolean;
  };
}

const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({ user }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [showComplaintModal, setShowComplaintModal] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { accountStatus, totalPendingAmount, pendingOrderCount, loadInitialData, error } = useStripeConnect();

  // Load Stripe data only when dropdown is opened
  useEffect(() => {
    if (isOpen) {
      loadInitialData();
    }
  }, [isOpen, loadInitialData]);

  const menuItems = [
    {
      icon: User,
      label: 'Profile',
      path: '/profile',
      description: 'View and edit your profile'
    },
    {
      icon: Package,
      label: 'Orders Management',
      path: '/orders',
      description: 'Manage your purchases and sales'
    },
    {
      icon: Settings,
      label: 'Settings',
      path: '/settings',
      description: 'App preferences and configuration'
    },
    {
      icon: Star,
      label: 'Feedback',
      action: () => {
        setIsOpen(false);
        setShowFeedbackModal(true);
      },
      description: 'Share your feedback with us'
    },
    {
      icon: Flag,
      label: 'Raise Complaint',
      action: () => {
        setIsOpen(false);
        setShowComplaintModal(true);
      },
      description: 'Report an issue or concern'
    },
    {
      icon: Info,
      label: 'About Us',
      path: '/about',
      description: 'Learn more about Hive Campus'
    },
    {
      icon: Phone,
      label: 'Contact Us',
      path: '/contact',
      description: 'Get in touch with our team'
    },
    {
      icon: MessageSquare,
      label: 'Feedback',
      path: '/feedback',
      description: 'Share your thoughts and suggestions'
    },
    {
      icon: AlertTriangle,
      label: 'Raise a Complaint',
      path: '/report',
      description: 'Report an issue or concern'
    },
    {
      icon: Settings,
      label: 'Settings',
      path: '/settings',
      description: 'Manage your account preferences'
    }
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await logOut();
      setIsOpen(false);
      // User will be redirected automatically by the auth state change
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
      alert('Error signing out. Please try again.');
    } finally {
      setIsSigningOut(false);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Profile Button - Optimized for mobile */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 md:space-x-3 p-1.5 md:p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group"
      >
        <div className="relative">
          <img
            src={user.avatar}
            alt={user.name}
            className="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 group-hover:border-primary-300 dark:group-hover:border-primary-500 transition-colors"
          />
          {user.verified && (
            <div className="absolute -bottom-0.5 -right-0.5 md:-bottom-1 md:-right-1 w-3 h-3 md:w-4 md:h-4 bg-primary-500 rounded-full flex items-center justify-center">
              <Shield className="w-1.5 h-1.5 md:w-2.5 md:h-2.5 text-white" />
            </div>
          )}
          {/* Online Status */}
          <div className="absolute bottom-0 right-0 w-2.5 h-2.5 md:w-3 md:h-3 bg-success-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
        </div>
        
        {/* User Info (Hidden on small mobile, visible on larger screens) */}
        <div className="hidden sm:block text-left">
          <p className="text-sm font-semibold text-gray-900 dark:text-white">
            {user.name}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 hidden md:block">
            {user.university}
          </p>
        </div>
        
        <ChevronDown className={`w-3 h-3 md:w-4 md:h-4 text-gray-400 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {/* Dropdown Menu - Mobile optimized */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-72 sm:w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-xl z-50 overflow-hidden animate-scale-in">
          {/* User Header */}
          <div className="p-4 sm:p-6 bg-gradient-to-r from-primary-500 to-accent-500 text-white">
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="relative">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-12 h-12 sm:w-16 sm:h-16 rounded-full object-cover border-3 border-white/20"
                />
                {user.verified && (
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4 text-primary-500" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-base sm:text-lg font-bold truncate">{user.name}</h3>
                <p className="text-xs sm:text-sm text-blue-100 truncate">{user.email}</p>
                <p className="text-xs text-blue-200 mt-1 truncate">{user.university}</p>
                {user.verified && (
                  <div className="flex items-center space-x-1 mt-2">
                    <Shield className="w-3 h-3" />
                    <span className="text-xs">Verified Student</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Onboarding Status */}
          {accountStatus !== null && !accountStatus.isOnboarded && (
            <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
              <OnboardingStatusBadge
                userId={user.email} // Using email as userId for now
                isOnboarded={accountStatus.isOnboarded}
                pendingAmount={totalPendingAmount}
                orderCount={pendingOrderCount}
                onSetupClick={() => {
                  setIsOpen(false);
                  navigate('/settings/payment');
                }}
                variant="compact"
                className="w-full"
              />
            </div>
          )}

          {/* Menu Items */}
          <div className="py-2 max-h-64 sm:max-h-80 overflow-y-auto">
            {menuItems.map((item, index) => {
              const IconComponent = item.icon;

              if (item.action) {
                // Render as button for action items
                return (
                  <button
                    key={index}
                    onClick={item.action}
                    className="w-full flex items-center space-x-3 sm:space-x-4 px-4 sm:px-6 py-3 sm:py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group text-left"
                  >
                    <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center group-hover:bg-primary-100 dark:group-hover:bg-primary-900/20 transition-colors flex-shrink-0">
                      <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                        {item.label}
                      </p>
                      <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">
                        {item.description}
                      </p>
                    </div>
                  </button>
                );
              }

              // Render as Link for path items
              return (
                <Link
                  key={index}
                  to={item.path!}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 sm:space-x-4 px-4 sm:px-6 py-3 sm:py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"
                >
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center group-hover:bg-primary-100 dark:group-hover:bg-primary-900/20 transition-colors flex-shrink-0">
                    <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {item.label}
                    </p>
                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">
                      {item.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>

          {/* Divider */}
          <div className="border-t border-gray-200 dark:border-gray-700"></div>

          {/* Sign Out */}
          <div className="p-2">
            <button
              onClick={handleSignOut}
              disabled={isSigningOut}
              className="w-full flex items-center space-x-3 sm:space-x-4 px-4 sm:px-6 py-3 sm:py-4 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors group rounded-xl disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center group-hover:bg-red-200 dark:group-hover:bg-red-900/40 transition-colors flex-shrink-0">
                {isSigningOut ? (
                  <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <LogOut className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 dark:text-red-400" />
                )}
              </div>
              <div className="flex-1 text-left">
                <p className="text-sm sm:text-base font-semibold text-red-600 dark:text-red-400">
                  {isSigningOut ? 'Signing Out...' : 'Sign Out'}
                </p>
                <p className="text-xs sm:text-sm text-red-500 dark:text-red-500">
                  Sign out of your account
                </p>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* Modals */}
      <FeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
      />

      <ComplaintModal
        isOpen={showComplaintModal}
        onClose={() => setShowComplaintModal(false)}
      />
    </div>
  );
};

export default UserProfileDropdown;