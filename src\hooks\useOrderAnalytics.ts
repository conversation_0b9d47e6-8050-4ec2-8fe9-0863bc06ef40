import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';
import { Order } from '../firebase/types';

interface OrderAnalytics {
  totalSpent: number;
  totalPurchases: number;
  totalRevenue: number;
  totalSales: number;
  averageOrderValue: number;
  monthlySpent: number;
  monthlyRevenue: number;
  topCategories: { category: string; count: number; amount: number }[];
  recentActivity: {
    totalOrders: number;
    completedOrders: number;
    pendingOrders: number;
  };
}

interface UseOrderAnalyticsReturn {
  analytics: OrderAnalytics | null;
  isLoading: boolean;
  error: string | null;
  refreshAnalytics: () => Promise<void>;
}

export const useOrderAnalytics = (): UseOrderAnalyticsReturn => {
  const { currentUser } = useAuth();
  const [analytics, setAnalytics] = useState<OrderAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const calculateAnalytics = useCallback(async () => {
    if (!currentUser) {
      setAnalytics(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch buyer orders (purchases)
      const buyerQuery = query(
        collection(db, 'orders'),
        where('buyerId', '==', currentUser.uid)
      );
      const buyerSnapshot = await getDocs(buyerQuery);
      const buyerOrders: Order[] = [];
      buyerSnapshot.forEach((doc) => {
        buyerOrders.push({ id: doc.id, ...doc.data() } as Order);
      });

      // Fetch seller orders (sales)
      const sellerQuery = query(
        collection(db, 'orders'),
        where('sellerId', '==', currentUser.uid)
      );
      const sellerSnapshot = await getDocs(sellerQuery);
      const sellerOrders: Order[] = [];
      sellerSnapshot.forEach((doc) => {
        sellerOrders.push({ id: doc.id, ...doc.data() } as Order);
      });

      // Calculate buyer analytics (spending)
      const totalSpent = buyerOrders.reduce((sum, order) => {
        return sum + (order.finalStripeAmount || order.amount || 0);
      }, 0);

      const totalPurchases = buyerOrders.length;

      // Calculate seller analytics (revenue)
      const totalRevenue = sellerOrders.reduce((sum, order) => {
        return sum + (order.sellerAmount || order.amount || 0);
      }, 0);

      const totalSales = sellerOrders.filter(order => 
        order.status === 'completed' || order.status === 'delivered'
      ).length;

      // Calculate average order value
      const averageOrderValue = totalPurchases > 0 ? totalSpent / totalPurchases : 0;

      // Calculate monthly metrics (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const monthlyBuyerOrders = buyerOrders.filter(order => {
        const orderDate = order.createdAt?.toDate?.() || new Date(order.createdAt);
        return orderDate >= thirtyDaysAgo;
      });

      const monthlySellerOrders = sellerOrders.filter(order => {
        const orderDate = order.createdAt?.toDate?.() || new Date(order.createdAt);
        return orderDate >= thirtyDaysAgo;
      });

      const monthlySpent = monthlyBuyerOrders.reduce((sum, order) => {
        return sum + (order.finalStripeAmount || order.amount || 0);
      }, 0);

      const monthlyRevenue = monthlySellerOrders.reduce((sum, order) => {
        return sum + (order.sellerAmount || order.amount || 0);
      }, 0);

      // Calculate top categories
      const categoryMap = new Map<string, { count: number; amount: number }>();
      
      [...buyerOrders, ...sellerOrders].forEach(order => {
        const category = order.category || 'Other';
        const existing = categoryMap.get(category) || { count: 0, amount: 0 };
        categoryMap.set(category, {
          count: existing.count + 1,
          amount: existing.amount + (order.amount || 0)
        });
      });

      const topCategories = Array.from(categoryMap.entries())
        .map(([category, data]) => ({ category, ...data }))
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 5);

      // Calculate recent activity
      const allOrders = [...buyerOrders, ...sellerOrders];
      const recentActivity = {
        totalOrders: allOrders.length,
        completedOrders: allOrders.filter(order => 
          order.status === 'completed' || order.status === 'delivered'
        ).length,
        pendingOrders: allOrders.filter(order => 
          order.status === 'pending_payment' || order.status === 'payment_completed'
        ).length,
      };

      const analyticsData: OrderAnalytics = {
        totalSpent,
        totalPurchases,
        totalRevenue,
        totalSales,
        averageOrderValue,
        monthlySpent,
        monthlyRevenue,
        topCategories,
        recentActivity,
      };

      setAnalytics(analyticsData);
    } catch (err) {
      console.error('Error calculating order analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to calculate analytics');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  const refreshAnalytics = useCallback(async () => {
    await calculateAnalytics();
  }, [calculateAnalytics]);

  useEffect(() => {
    calculateAnalytics();
  }, [calculateAnalytics]);

  return {
    analytics,
    isLoading,
    error,
    refreshAnalytics,
  };
};
