{"timestamp": "2025-07-22T19:17:51.325Z", "overallScore": 78, "passed": 25, "failed": 3, "warnings": 4, "categories": {"firebase-rules": {"tests": [{"test": "No unsafe global read/write rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:51.459Z"}, {"test": "Authentication checks implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:51.459Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:51.459Z"}, {"test": "User ownership validation present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:51.459Z"}, {"test": "Input validation in security rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:51.460Z"}], "score": 100}, "secrets": {"tests": [{"test": "No hardcoded secrets in source code", "status": "FAIL", "severity": "Critical", "details": null, "timestamp": "2025-07-22T19:17:54.229Z"}, {"test": "All required environment variables present", "status": "FAIL", "severity": "Critical", "details": "Missing: VITE_FIREBASE_API_KEY, VITE_FIREBASE_AUTH_DOMAIN, VITE_FIREBASE_PROJECT_ID, VITE_FIREBASE_STORAGE_BUCKET, VITE_FIREBASE_MESSAGING_SENDER_ID, VITE_FIREBASE_APP_ID, VITE_STRIPE_PUBLISHABLE_KEY", "timestamp": "2025-07-22T19:17:54.230Z"}, {"test": "No .env files committed to repository", "status": "FAIL", "severity": "High", "details": "Found: .env, .env.production", "timestamp": "2025-07-22T19:17:54.231Z"}], "score": 0}, "dependencies": {"tests": [{"test": "No critical vulnerabilities", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:57.086Z"}, {"test": "No high-severity vulnerabilities", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:57.086Z"}, {"test": "Moderate vulnerabilities under control", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:57.086Z"}, {"test": "Some dependencies may be outdated", "status": "WARN", "severity": "Low", "details": null, "timestamp": "2025-07-22T19:17:59.737Z"}], "score": 75}, "client-security": {"tests": [{"test": "Security utilities implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:59.738Z"}, {"test": "Input sanitization implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:59.738Z"}, {"test": "HTTPS enforcement implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:59.739Z"}, {"test": "Client-side rate limiting implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:59.739Z"}, {"test": "Security initialization in main app", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:17:59.739Z"}], "score": 100}, "network-security": {"tests": [{"test": "Security headers configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:18:00.769Z"}, {"test": "HTTPS configuration present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.769Z"}, {"test": "Firebase hosting security headers configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.770Z"}, {"test": "HSTS header configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:18:00.770Z"}], "score": 50}, "data-privacy": {"tests": [{"test": "Privacy policy page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.771Z"}, {"test": "Terms and conditions page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.771Z"}, {"test": "Consent management implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.819Z"}, {"test": "Data deletion policies in Firebase rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.819Z"}], "score": 100}, "csp": {"tests": [{"test": "CSP manager implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.820Z"}, {"test": "Nonce generation for inline scripts", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.820Z"}, {"test": "CSP meta tag in HTML", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-22T19:18:00.820Z"}], "score": 67}, "authentication": {"tests": [{"test": "Authentication context implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.821Z"}, {"test": "Protected route component implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.821Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.821Z"}, {"test": "Firebase Auth domain configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-22T19:18:00.822Z"}], "score": 100}}}